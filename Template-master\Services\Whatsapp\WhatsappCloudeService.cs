﻿using Core;
using Core.Entities;
using Core.Helper;
using Core.Resluts;
using Core.Service.Contract;
using Core.Specifications.ChatSpec;
using Core.Specifications.CustomerSpec;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Services.Whatsapp
{
    public class WhatsappCloudeService : IWhatsAppCloudeService
    {
        private readonly WhatsAppCloudeSettings _settings;
        private readonly IUnitOfWork _unitOfWork;

        public WhatsappCloudeService(IOptions<WhatsAppCloudeSettings> settings,IUnitOfWork unitOfWork)
        {
            _settings = settings.Value;
            this._unitOfWork = unitOfWork;
        }

        public async Task RecieveMessage(string mobile, string message , string name)
        {
            //chick if customer exist
            var spec = new GetCustomerByPhone(mobile);
            var customer = await _unitOfWork.GetRepository<Customer>().GetByIdSpecAsync(spec);
            if (customer is null)
            {
                //create new customer
                customer = new Customer
                {
                    Name = name,
                    Number = mobile,
                  
                };
                var chat = new Chat
                {
                    LastMessageAt = DateTime.UtcNow,
                    CustomerId = customer.Id,
                };
                await _unitOfWork.GetRepository<Chat>().AddAsync(chat);
                await _unitOfWork.CompleteAsync();
                //ToDo type
               var chatMessage = new Message
                {
                    ChatId = chat.Id,
                    Content = message,
                    IsSent = false,
                    CreatedAt = DateTime.UtcNow,
                    IsRead = false,
                    IsDelivered = true,
                    MessageType = "text",
                };

                await _unitOfWork.GetRepository<Message>().AddAsync(chatMessage);
                await _unitOfWork.GetRepository<Customer>().AddAsync(customer);

                await _unitOfWork.CompleteAsync();
            }
            else
            {
                var chatSpec = new GetChatWithCustomer(customer.Id);
                var chat = await _unitOfWork.GetRepository<Chat>().GetByIdSpecAsync(chatSpec);
                var chatMessage = new Message
                {
                    ChatId = chat.Id,
                    Content = message,
                    IsSent = false,
                    CreatedAt = DateTime.UtcNow,
                    IsRead = false,
                    IsDelivered = true,
                    MessageType = "text",

                };
                chat.LastMessageAt = DateTime.UtcNow;
                chat.LastMessage = message;
                chat.UnreadCount += 1;
                await _unitOfWork.GetRepository<Message>().AddAsync(chatMessage);
                await _unitOfWork.CompleteAsync();
            }
        }

        //public async Task<bool> SendMessage(string mobile, string language, string template, List<WhatsAppComponent>? components = null)
        //{
        //    using HttpClient httpClient = new();

        //    httpClient.DefaultRequestHeaders.Authorization =
        //        new AuthenticationHeaderValue("Bearer", _settings.Token);

        //    WhatsAppRequest body = new()
        //    {
        //        to = mobile,
        //        template = new Template
        //        {
        //            name = template,
        //            language = new Language { code = language }
        //        }
        //    };

        //    if (components is not null)
        //        body.template.components = components;

        //    HttpResponseMessage response =
        //        await httpClient.PostAsJsonAsync(new Uri(_settings.ApiUrl), body);

        //    return response.IsSuccessStatusCode;
        //}

        public async Task<bool> SendTextMessage(string mobile, string message)
        {
            using HttpClient httpClient = new();

            httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Bearer", _settings.Token);

            var body = new WhatsAppTextRequest
            {
                to = mobile,
                text = new WhatsAppText
                {
                    body = message
                }
            };

            HttpResponseMessage response =
                await httpClient.PostAsJsonAsync(new Uri(_settings.ApiUrl), body);

            return response.IsSuccessStatusCode;
        }

    }
}
