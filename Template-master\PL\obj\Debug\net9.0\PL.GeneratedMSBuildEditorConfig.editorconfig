is_global = true
build_property.TargetFramework = net9.0
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = PL
build_property.RootNamespace = PL
build_property.ProjectDir = D:\NET\Projects\templatewithView\Template-master\PL\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\NET\Projects\templatewithView\Template-master\PL
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Auth/ForgotPassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQXV0aFxGb3Jnb3RQYXNzd29yZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Auth/ForgotPasswordConfirmation.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQXV0aFxGb3Jnb3RQYXNzd29yZENvbmZpcm1hdGlvbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Auth/Login.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQXV0aFxMb2dpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Auth/Register.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQXV0aFxSZWdpc3Rlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Auth/ResetPassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQXV0aFxSZXNldFBhc3N3b3JkLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Auth/ResetPasswordConfirmation.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQXV0aFxSZXNldFBhc3N3b3JkQ29uZmlybWF0aW9uLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxQcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Notification/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcTm90aWZpY2F0aW9uXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Shared/_AuthLayout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9BdXRoTGF5b3V0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/User/ChangePassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxDaGFuZ2VQYXNzd29yZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/User/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxDcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/User/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxEZWxldGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/User/DepartmentUsers.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxEZXBhcnRtZW50VXNlcnMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/User/Details.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxEZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/User/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxFZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/User/EditProfile.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxFZGl0UHJvZmlsZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/User/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/User/ManageRoles.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxNYW5hZ2VSb2xlcy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/User/Profile.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxQcm9maWxlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/User/_DepartmentUserRow.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxfRGVwYXJ0bWVudFVzZXJSb3cuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Whatsapp/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcV2hhdHNhcHBcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Whatsapp/ServiceCustomers.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcV2hhdHNhcHBcU2VydmljZUN1c3RvbWVycy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Whatsapp/Settings.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcV2hhdHNhcHBcU2V0dGluZ3MuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/NET/Projects/templatewithView/Template-master/PL/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-ykycstrbf1
