using Core;
using Core.Entities;
using Core.Service.Contract;
using Core.Specifications.ChatSpec;
using Core.Specifications.MessageSpec;
using Microsoft.Extensions.Logging;

namespace Services.Whatsapp
{
    public class WhatsAppService : IWhatsAppService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAutoReplyService _autoReplyService;
        private readonly ILogger<WhatsAppService> _logger;

        public WhatsAppService(IUnitOfWork unitOfWork, IAutoReplyService autoReplyService, ILogger<WhatsAppService> logger)
        {
            _unitOfWork = unitOfWork;
            _autoReplyService = autoReplyService;
            _logger = logger;
        }

        #region Chat Management

        public async Task<IEnumerable<Chat>> GetAllChatsAsync()
        {
            try
            {
                var spec = new GetAllChats(); 
                var reslut = await _unitOfWork.GetRepository<Chat>().GetAllSpecAsync(spec);
                return reslut;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all chats");
                throw;
            }
        }


        public async Task<IEnumerable<Chat>> GetChatsByAssignedUserAsync(string userId)
        {
            try
            {
                var spec = new GetChatsByAssignedUser(userId);
                return await _unitOfWork.GetRepository<Chat>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chats by assigned user: {UserId}", userId);
                throw;
            }
        }

        public async Task<IEnumerable<Chat>> SearchChatsAsync(string searchTerm)
        {
            try
            {
                var spec = new SearchChats(searchTerm);
                return await _unitOfWork.GetRepository<Chat>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching chats with term: {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<Chat?> GetChatByIdAsync(int chatId, bool includeMessages = true)
        {
            try
            {
                var spec = new GetChatById(chatId, includeMessages);
                return await _unitOfWork.GetRepository<Chat>().GetByIdSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chat by ID: {ChatId}", chatId);
                throw;
            }
        }

        public async Task<Chat?> GetChatByPhoneAsync(string phoneNumber, bool includeMessages = true)
        {
            try
            {
                var spec = new GetChatByPhone(phoneNumber, includeMessages);
                return await _unitOfWork.GetRepository<Chat>().GetByIdSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chat by phone: {PhoneNumber}", phoneNumber);
                throw;
            }
        }

        public async Task<Chat> CreateOrUpdateChatAsync(string customerName, string customerPhone, string? customerEmail = null)
        {
            try
            {
                // Check if chat already exists
                var existingChat = await GetChatByPhoneAsync(customerPhone, false);
                
                if (existingChat != null)
                {
                    // Update existing chat
                    existingChat.Customer.Number = customerName;  
                    existingChat.LastMessageAt = DateTime.UtcNow;
                    
                    _unitOfWork.GetRepository<Chat>().Update(existingChat);
                    await _unitOfWork.CompleteAsync();
                    
                    _logger.LogInformation("Updated existing chat for phone: {PhoneNumber}", customerPhone);
                    return existingChat;
                }

                var customer = new Customer
                {
                    Name = customerName,
                    Number = customerPhone,
               
                };
                // Create new chat
                var newChat = new Chat
                {
                    Customer = customer,
                    LastMessageAt = DateTime.UtcNow,
       
                };

                await _unitOfWork.GetRepository<Chat>().AddAsync(newChat);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Created new chat for phone: {PhoneNumber}", customerPhone);
                return newChat;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating or updating chat for phone: {PhoneNumber}", customerPhone);
                throw;
            }
        }

        public async Task<bool> UpdateChatStatusAsync(int chatId, string status)
        {
            try
            {
                var chat = await _unitOfWork.GetRepository<Chat>().GetByIdAsync(chatId);
                if (chat == null) return false;

                _unitOfWork.GetRepository<Chat>().Update(chat);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Updated chat status: ChatId={ChatId}, Status={Status}", chatId, status);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating chat status: ChatId={ChatId}", chatId);
                return false;
            }
        }

        public async Task<bool> AssignChatToUserAsync(int chatId, string userId)
        {
            try
            {
                var chat = await _unitOfWork.GetRepository<Chat>().GetByIdAsync(chatId);
                if (chat == null) return false;

                chat.AssignedUserId = userId;
                _unitOfWork.GetRepository<Chat>().Update(chat);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Assigned chat to user: ChatId={ChatId}, UserId={UserId}", chatId, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning chat to user: ChatId={ChatId}, UserId={UserId}", chatId, userId);
                return false;
            }
        }

        public async Task<bool> ArchiveChatAsync(int chatId)
        {
            try
            {
                var chat = await _unitOfWork.GetRepository<Chat>().GetByIdAsync(chatId);
                if (chat == null) return false;

              
                _unitOfWork.GetRepository<Chat>().Update(chat);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Archived chat: ChatId={ChatId}", chatId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving chat: ChatId={ChatId}", chatId);
                return false;
            }
        }

        public async Task<bool> UnarchiveChatAsync(int chatId)
        {
            try
            {
                var chat = await _unitOfWork.GetRepository<Chat>().GetByIdAsync(chatId);
                if (chat == null) return false;

                _unitOfWork.GetRepository<Chat>().Update(chat);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Unarchived chat: ChatId={ChatId}", chatId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unarchiving chat: ChatId={ChatId}", chatId);
                return false;
            }
        }

        public async Task<bool> DeleteChatAsync(int chatId)
        {
            try
            {
                var chat = await _unitOfWork.GetRepository<Chat>().GetByIdAsync(chatId);
                if (chat == null) return false;

                _unitOfWork.GetRepository<Chat>().Delete(chat);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Deleted chat: ChatId={ChatId}", chatId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting chat: ChatId={ChatId}", chatId);
                return false;
            }
        }

        #endregion

        #region Message Management

        public async Task<IEnumerable<Message>> GetMessagesByChatIdAsync(int chatId, int skip = 0, int take = 50)
        {
            try
            {
                var spec = new GetMessagesByChatId(chatId, skip, take);
                return await _unitOfWork.GetRepository<Message>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages by chat ID: {ChatId}", chatId);
                throw;
            }
        }

        public async Task<IEnumerable<Message>> GetMessagesByChatIdFromDateAsync(int chatId, DateTime fromDate)
        {
            try
            {
                var spec = new GetMessagesByChatId(chatId, fromDate);
                return await _unitOfWork.GetRepository<Message>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages by chat ID from date: {ChatId}, {FromDate}", chatId, fromDate);
                throw;
            }
        }

        public async Task<IEnumerable<Message>> GetUnreadMessagesAsync()
        {
            try
            {
                var spec = new GetUnreadMessages();
                return await _unitOfWork.GetRepository<Message>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread messages");
                throw;
            }
        }

        public async Task<IEnumerable<Message>> GetUnreadMessagesByChatIdAsync(int chatId)
        {
            try
            {
                var spec = new GetUnreadMessages(chatId);
                return await _unitOfWork.GetRepository<Message>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread messages by chat ID: {ChatId}", chatId);
                throw;
            }
        }

        public async Task<IEnumerable<Message>> GetUnreadMessagesByUserAsync(string userId)
        {
            try
            {
                var spec = new GetUnreadMessages(userId);
                return await _unitOfWork.GetRepository<Message>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread messages by user: {UserId}", userId);
                throw;
            }
        }

        public async Task<Message> SendMessageAsync(int chatId, string content, string? sentByUserId = null, string messageType = "text")
        {
            try
            {
                var message = new Message
                {
                    ChatId = chatId,
                    Content = content,
                    IsSent = true,
                    CreatedAt = DateTime.UtcNow,
                    IsRead = true,
                    IsDelivered = true,
                    MessageType = messageType,
                    SentByUserId = sentByUserId
                };

                await _unitOfWork.GetRepository<Message>().AddAsync(message);

                // Update chat's last message info
                var chat = await _unitOfWork.GetRepository<Chat>().GetByIdAsync(chatId);
                if (chat != null)
                {
                    chat.LastMessage = content.Length > 100 ? content.Substring(0, 100) + "..." : content;
                    chat.LastMessageAt = DateTime.UtcNow;
                    _unitOfWork.GetRepository<Chat>().Update(chat);
                }

                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Sent message: ChatId={ChatId}, UserId={UserId}", chatId, sentByUserId);
                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message: ChatId={ChatId}", chatId);
                throw;
            }
        }

        public async Task<Message> ReceiveMessageAsync(int chatId, string content, string messageType = "text")
        {
            try
            {
                var message = new Message
                {
                    ChatId = chatId,
                    Content = content,
                    IsSent = false,
                    CreatedAt = DateTime.UtcNow,
                    IsRead = false,
                    IsDelivered = true,
                    MessageType = messageType
                };

                await _unitOfWork.GetRepository<Message>().AddAsync(message);

                // Update chat's last message info and unread count
                var chat = await _unitOfWork.GetRepository<Chat>().GetByIdAsync(chatId);
                if (chat != null)
                {
                    chat.LastMessage = content.Length > 100 ? content.Substring(0, 100) + "..." : content;
                    chat.LastMessageAt = DateTime.UtcNow;
                    chat.UnreadCount++;
                    _unitOfWork.GetRepository<Chat>().Update(chat);
                }

                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Received message: ChatId={ChatId}", chatId);

                // Process auto reply if enabled
                await SendAutoReplyAsync(chatId, content);

                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error receiving message: ChatId={ChatId}", chatId);
                throw;
            }
        }

        public async Task<bool> MarkMessageAsReadAsync(int messageId)
        {
            try
            {
                var message = await _unitOfWork.GetRepository<Message>().GetByIdAsync(messageId);
                if (message == null || message.IsRead) return false;

                message.IsRead = true;
                _unitOfWork.GetRepository<Message>().Update(message);

                // Update chat's unread count
                var chat = await _unitOfWork.GetRepository<Chat>().GetByIdAsync(message.ChatId);
                if (chat != null && chat.UnreadCount > 0)
                {
                    chat.UnreadCount--;
                    _unitOfWork.GetRepository<Chat>().Update(chat);
                }

                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Marked message as read: MessageId={MessageId}", messageId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking message as read: MessageId={MessageId}", messageId);
                return false;
            }
        }

        public async Task<bool> MarkMessagesAsReadAsync(int chatId)
        {
            try
            {
                var unreadMessages = await GetUnreadMessagesByChatIdAsync(chatId);

                foreach (var message in unreadMessages)
                {
                    message.IsRead = true;
                    _unitOfWork.GetRepository<Message>().Update(message);
                }

                // Reset chat's unread count
                var chat = await _unitOfWork.GetRepository<Chat>().GetByIdAsync(chatId);
                if (chat != null)
                {
                    chat.UnreadCount = 0;
                    _unitOfWork.GetRepository<Chat>().Update(chat);
                }

                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Marked all messages as read: ChatId={ChatId}", chatId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking messages as read: ChatId={ChatId}", chatId);
                return false;
            }
        }

        public async Task<bool> DeleteMessageAsync(int messageId)
        {
            try
            {
                var message = await _unitOfWork.GetRepository<Message>().GetByIdAsync(messageId);
                if (message == null) return false;

                _unitOfWork.GetRepository<Message>().Delete(message);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Deleted message: MessageId={MessageId}", messageId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting message: MessageId={MessageId}", messageId);
                return false;
            }
        }

        #endregion

        #region Auto Reply Management

        public async Task<string?> ProcessAutoReplyAsync(string messageContent)
        {
            try
            {
                return await _autoReplyService.ProcessMessageForAutoReplyAsync(messageContent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing auto reply for message: {MessageContent}", messageContent);
                return null;
            }
        }

        public async Task<bool> SendAutoReplyAsync(int chatId, string messageContent)
        {
            try
            {
                var autoReplyResponse = await ProcessAutoReplyAsync(messageContent);
                if (string.IsNullOrEmpty(autoReplyResponse)) return false;

                var autoReply = await _autoReplyService.FindMatchingAutoReplyAsync(messageContent);
                if (autoReply == null) return false;

                var message = new Message
                {
                    ChatId = chatId,
                    Content = autoReplyResponse,
                    IsSent = true,
                    CreatedAt = DateTime.UtcNow,
                    IsRead = true,
                    IsDelivered = true,
                    MessageType = "text",
         
                };

                await _unitOfWork.GetRepository<Message>().AddAsync(message);

                // Update chat's last message info
                var chat = await _unitOfWork.GetRepository<Chat>().GetByIdAsync(chatId);
                if (chat != null)
                {
                    chat.LastMessage = autoReplyResponse.Length > 100 ? autoReplyResponse.Substring(0, 100) + "..." : autoReplyResponse;
                    chat.LastMessageAt = DateTime.UtcNow;
                    _unitOfWork.GetRepository<Chat>().Update(chat);
                }

                await _unitOfWork.CompleteAsync();

                // Update auto reply usage
                await _autoReplyService.UpdateAutoReplyUsageAsync(autoReply.Id);

                _logger.LogInformation("Sent auto reply: ChatId={ChatId}, AutoReplyId={AutoReplyId}", chatId, autoReply.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending auto reply: ChatId={ChatId}", chatId);
                return false;
            }
        }

        #endregion

        #region Statistics

        public async Task<int> GetTotalChatsCountAsync()
        {
            try
            {
                var spec = new GetAllChats(); // Include archived
                return await _unitOfWork.GetRepository<Chat>().CountSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total chats count");
                return 0;
            }
        }

        public async Task<int> GetActiveChatsCountAsync()
        {
            try
            {
                var spec = new GetAllChats();
                return await _unitOfWork.GetRepository<Chat>().CountSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active chats count");
                return 0;
            }
        }

        public async Task<int> GetPendingChatsCountAsync()
        {
            try
            {
                var spec = new GetAllChats();
                return await _unitOfWork.GetRepository<Chat>().CountSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending chats count");
                return 0;
            }
        }

        public async Task<int> GetInactiveChatsCountAsync()
        {
            try
            {
                var spec = new GetAllChats();
                return await _unitOfWork.GetRepository<Chat>().CountSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inactive chats count");
                return 0;
            }
        }

        public async Task<int> GetUnreadMessagesCountAsync()
        {
            try
            {
                var spec = new GetUnreadMessages();
                return await _unitOfWork.GetRepository<Message>().CountSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread messages count");
                return 0;
            }
        }

        public async Task<int> GetUnreadMessagesCountByUserAsync(string userId)
        {
            try
            {
                var spec = new GetUnreadMessages(userId);
                return await _unitOfWork.GetRepository<Message>().CountSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread messages count by user: {UserId}", userId);
                return 0;
            }
        }

        #endregion

    }
}
