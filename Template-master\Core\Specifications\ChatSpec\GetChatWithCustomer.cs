﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Specifications.ChatSpec
{
    public class GetChatWithCustomer : Specification<Entities.Chat>
    {
        public GetChatWithCustomer(int customerId) : base(c => c.CustomerId == customerId)
        {
            includes.Add(c => c.Customer);
          
           
        }

    }
}
