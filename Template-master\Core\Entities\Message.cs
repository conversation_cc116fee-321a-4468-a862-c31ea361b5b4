using System.ComponentModel.DataAnnotations;

namespace Core.Entities
{
    public class Message : BaseEntity
    {
        [Required]
        public int ChatId { get; set; }

        [Required]
        [MaxLength(2000)]
        public string Content { get; set; } = string.Empty;

        public bool IsSent { get; set; } = false; // true = sent by us, false = received from customer

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public bool IsRead { get; set; } = false;

        public bool IsDelivered { get; set; } = false;

        [MaxLength(20)]
        public string MessageType { get; set; } = "text"; // text, image, file, audio, video

        [MaxLength(500)]
        public string? AttachmentUrl { get; set; }

        [MaxLength(100)]
        public string? AttachmentName { get; set; }

        public long? AttachmentSize { get; set; }

        // User who sent the message (for internal messages)
        public string? SentByUserId { get; set; }
        public virtual User? SentByUser { get; set; }

        // Navigation properties
        public virtual Chat Chat { get; set; } = null!;
       }
}
