﻿
.reset-password-container {
    max-width: 650px;
    width: 100%;
    margin: 0 auto;
}

.card {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    overflow: hidden;
    animation: fadeIn 0.8s ease-in-out;
}

@@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.lock-icon {
    background-color: rgba(13, 110, 253, 0.1);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    animation: pulse 2s infinite;
}

@@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4);
    }

    70% {
        box-shadow: 0 0 0 15px rgba(13, 110, 253, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

.form-control {
    border-radius: 8px;
    transition: all 0.3s;
}

    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

.btn-primary {
    background: linear-gradient(45deg, #2937f0, #9f1ae2);
    border: none;
    border-radius: 8px;
    transition: all 0.3s;
}

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
        background: linear-gradient(45deg, #1e2bd1, #8614c0);
    }

/* For RTL support with input fields */
input[dir="ltr"] {
    text-align: left;
}

/* Password strength indicator */
.valid-requirement {
    color: #198754;
}

    .valid-requirement i {
        animation: spin 0.5s ease-out;
    }

@@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}


