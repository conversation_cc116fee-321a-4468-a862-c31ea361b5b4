using Core.Entities;

namespace Core.Service.Contract
{
    public interface IServiceNumberService
    {
        // Service number management
        Task<IEnumerable<ServiceNumber>> GetAllServiceNumbersAsync();
        Task<IEnumerable<ServiceNumber>> GetActiveServiceNumbersAsync();
        Task<IEnumerable<ServiceNumber>> GetServiceNumbersByTypeAsync(string serviceType);
        Task<IEnumerable<ServiceNumber>> GetServiceNumbersByUserAsync(string userId);
        Task<ServiceNumber?> GetServiceNumberByIdAsync(int id);
        Task<ServiceNumber> CreateServiceNumberAsync(string name, string phoneNumber, string? description = null, 
            string serviceType = "support", string? createdByUserId = null);
        Task<bool> UpdateServiceNumberAsync(int id, string name, string phoneNumber, string? description = null, 
            string serviceType = "support");
        Task<bool> ToggleServiceNumberStatusAsync(int id);
        Task<bool> UpdateServiceNumberOrderAsync(int id, int displayOrder);
        Task<bool> AssignUserToServiceNumberAsync(int serviceNumberId, string userId);
        Task<bool> UnassignUserFromServiceNumberAsync(int serviceNumberId, string userId);
        Task<bool> DeleteServiceNumberAsync(int id);
        Task<bool> UpdateContactCountAsync(int id);

        // Service number statistics
        Task<int> GetTotalServiceNumbersCountAsync();
        Task<int> GetActiveServiceNumbersCountAsync();
        Task<Dictionary<string, int>> GetServiceNumbersCountByTypeAsync();
    }
}
