using Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Repository.Data.Configurations
{
    public class AutoReplyConfiguration : IEntityTypeConfiguration<AutoReply>
    {
        public void Configure(EntityTypeBuilder<AutoReply> builder)
        {
            builder.HasKey(ar => ar.Id);

            builder.Property(ar => ar.Trigger)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(ar => ar.Response)
                .IsRequired()
                .HasMaxLength(2000);

            builder.Property(ar => ar.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(ar => ar.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");

            builder.Property(ar => ar.MatchType)
                .IsRequired()
                .HasMaxLength(20)
                .HasDefaultValue("contains");

            builder.Property(ar => ar.IsCaseSensitive)
                .HasDefaultValue(false);

            builder.Property(ar => ar.Priority)
                .HasDefaultValue(1);

            builder.Property(ar => ar.UsageCount)
                .HasDefaultValue(0);

            builder.Property(ar => ar.Description)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(ar => ar.CreatedByUser)
                .WithMany()
                .HasForeignKey(ar => ar.CreatedByUserId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasMany(ar => ar.Messages)
                .WithOne(m => m.AutoReply)
                .HasForeignKey(m => m.AutoReplyId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(ar => ar.Trigger);

            builder.HasIndex(ar => ar.IsActive);

            builder.HasIndex(ar => ar.Priority);

            builder.HasIndex(ar => ar.CreatedByUserId);

            builder.HasIndex(ar => new { ar.IsActive, ar.Priority });

            builder.HasIndex(ar => new { ar.Trigger, ar.IsActive });
        }
    }
}
