using Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Repository.Data.Configurations
{
    public class AutoReplyConfiguration : IEntityTypeConfiguration<AutoReply>
    {
        public void Configure(EntityTypeBuilder<AutoReply> builder)
        {
            builder.HasKey(ar => ar.Id);

            builder.Property(ar => ar.Trigger)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(ar => ar.Response)
                .IsRequired()
                .HasMaxLength(2000);

            builder.Property(ar => ar.IsActive)
                .IsRequired()
                .HasDefaultValue(true);




            // Relationships
            builder.HasOne(ar => ar.CreatedByUser)
                .WithMany()
                .HasForeignKey(ar => ar.CreatedByUserId)
                .OnDelete(DeleteBehavior.SetNull);

     
            // Indexes
            builder.HasIndex(ar => ar.Trigger);

            builder.HasIndex(ar => ar.IsActive);

            builder.HasIndex(ar => ar.Priority);

            builder.HasIndex(ar => ar.CreatedByUserId);

            builder.HasIndex(ar => new { ar.IsActive, ar.Priority });

            builder.HasIndex(ar => new { ar.Trigger, ar.IsActive });
        }
    }
}
