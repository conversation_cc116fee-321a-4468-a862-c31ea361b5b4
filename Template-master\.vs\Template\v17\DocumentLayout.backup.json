{"Version": 1, "WorkspaceRootPath": "D:\\NET\\Projects\\templatewithView\\Template-master\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\controllers\\whatsappcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\controllers\\whatsappcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\whatsapp\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\whatsapp\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\whatsapp\\settings.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\whatsapp\\settings.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\whatsapp\\servicecustomers.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\whatsapp\\servicecustomers.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\resetpasswordconfirmation.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\resetpasswordconfirmation.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\register.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\register.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\resetpasswordconfirmation.css||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\resetpasswordconfirmation.css||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\resetpassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\resetpassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\shared\\_authlayout.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\shared\\_authlayout.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\js\\auth\\resetpassword.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\js\\auth\\resetpassword.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\resetpassword.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\resetpassword.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\js\\auth\\rigester.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\js\\auth\\rigester.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\_viewimports.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\_viewimports.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\rigester.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\rigester.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\shared\\_layout.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\shared\\_layout.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\layout.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\layout.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\forgotpassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\forgotpassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\login.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\login.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\layout.css||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\layout.css||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\adminlte.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\adminlte.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\js\\adminlte.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\js\\adminlte.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\forgotpasswordconfirmation.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\forgotpasswordconfirmation.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\js\\auth\\login.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\js\\auth\\login.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\login.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\login.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\forgotpasswordconfirmation.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\forgotpasswordconfirmation.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\forgotpassword.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\forgotpassword.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\specificationprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\specificationprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\entities\\notification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\entities\\notification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\20250823141748_notification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Repository\\Data\\20250823141748_notification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\controllers\\notificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\controllers\\notificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\specifications\\notificationspec\\getbyuserid.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\specifications\\notificationspec\\getbyuserid.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\specifications\\specification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\specifications\\specification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\repositories\\genericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\repositories\\genericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\repository.contract\\igenericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\repository.contract\\igenericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\extentions\\applicationservicesextentions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\extentions\\applicationservicesextentions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\models\\notificationviewmodel\\notificationviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\models\\notificationviewmodel\\notificationviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\notification\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\notification\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\departmentusers.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\departmentusers.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\_departmentuserrow.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\_departmentuserrow.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\editprofile.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\editprofile.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\delete.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\delete.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\create.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\create.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\changepassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\changepassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\extentions\\dbpreprocessextention.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\extentions\\dbpreprocessextention.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\data\\dataseeding\\identityseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\data\\dataseeding\\identityseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\profile.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\profile.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\edit.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\edit.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\details.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\details.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\data\\migrations\\20250823142050_notification1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\data\\migrations\\20250823142050_notification1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{57d563b6-44a5-47df-85be-f4199ad6b651}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "WhatsappController.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\WhatsappController.cs", "RelativeDocumentMoniker": "PL\\Controllers\\WhatsappController.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\WhatsappController.cs", "RelativeToolTip": "PL\\Controllers\\WhatsappController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACIAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T12:53:51.962Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Settings.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\Settings.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Whatsapp\\Settings.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\Settings.cshtml", "RelativeToolTip": "PL\\Views\\Whatsapp\\Settings.cshtml", "ViewState": "AgIAAHkBAAAAAAAAAADwv4sBAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-25T12:48:46.48Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ServiceCustomers.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\ServiceCustomers.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Whatsapp\\ServiceCustomers.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\ServiceCustomers.cshtml", "RelativeToolTip": "PL\\Views\\Whatsapp\\ServiceCustomers.cshtml", "ViewState": "AgIAAPEAAAAAAAAAAADwvwMBAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-25T12:48:00.74Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Index.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\Index.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Whatsapp\\Index.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\Index.cshtml", "RelativeToolTip": "PL\\Views\\Whatsapp\\Index.cshtml", "ViewState": "AgIAABYBAAAAAAAAAADwvxwBAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-25T12:47:46.517Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ResetPasswordConfirmation.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T18:37:00.623Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ResetPasswordConfirmation.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ResetPasswordConfirmation.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\ResetPasswordConfirmation.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ResetPasswordConfirmation.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\ResetPasswordConfirmation.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T18:36:14.316Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ResetPassword.js", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\ResetPassword.js", "RelativeDocumentMoniker": "PL\\wwwroot\\js\\Auth\\ResetPassword.js", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\ResetPassword.js", "RelativeToolTip": "PL\\wwwroot\\js\\Auth\\ResetPassword.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-23T18:35:16.351Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "ResetPassword.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPassword.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\ResetPassword.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPassword.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\ResetPassword.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T18:33:24.941Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "_AuthLayout.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Shared\\_AuthLayout.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Shared\\_AuthLayout.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Shared\\_AuthLayout.cshtml", "RelativeToolTip": "PL\\Views\\Shared\\_AuthLayout.cshtml", "ViewState": "AgIAABsAAAAAAAAAAAAAACYAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T17:18:53.732Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "Rigester.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Rigester.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\Rigester.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Rigester.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\Rigester.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T17:06:12.493Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "Register.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\Register.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\Register.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\Register.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\Register.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:27:21.431Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "Rigester.js", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Rigester.js", "RelativeDocumentMoniker": "PL\\wwwroot\\js\\Auth\\Rigester.js", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Rigester.js", "RelativeToolTip": "PL\\wwwroot\\js\\Auth\\Rigester.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-23T17:07:31.63Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "adminlte.js", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.js", "RelativeDocumentMoniker": "PL\\wwwroot\\js\\adminlte.js", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.js", "RelativeToolTip": "PL\\wwwroot\\js\\adminlte.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-23T17:20:20.096Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "ResetPassword.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ResetPassword.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\ResetPassword.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ResetPassword.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\ResetPassword.cshtml", "ViewState": "AgIAAEQAAAAAAAAAAAAAAFAAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T17:05:03.819Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "_Layout.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Shared\\_Layout.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Shared\\_Layout.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Shared\\_Layout.cshtml", "RelativeToolTip": "PL\\Views\\Shared\\_Layout.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:26:24.695Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "_ViewImports.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\_ViewImports.cshtml", "RelativeDocumentMoniker": "PL\\Views\\_ViewImports.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\_ViewImports.cshtml", "RelativeToolTip": "PL\\Views\\_ViewImports.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:13:02.075Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "adminlte.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\adminlte.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.css", "RelativeToolTip": "PL\\wwwroot\\css\\adminlte.css", "ViewState": "AgIAAAPPAAAAAAAAAAAAABPPAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T17:20:25.253Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "Layout.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Layout.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\Layout.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Layout.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\Layout.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T18:26:04.567Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "Login.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\Login.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\Login.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\Login.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\Login.cshtml", "ViewState": "AgIAACoAAAAAAAAAAAAAADsAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T14:47:34.887Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "ForgotPassword.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ForgotPassword.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\ForgotPassword.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ForgotPassword.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\ForgotPassword.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T14:26:22.928Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "Layout.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Layout.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\Layout.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Layout.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\Layout.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T17:25:22.193Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "ForgotPasswordConfirmation.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ForgotPasswordConfirmation.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\ForgotPasswordConfirmation.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ForgotPasswordConfirmation.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\ForgotPasswordConfirmation.cshtml", "ViewState": "AgIAAAQAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T14:45:42.735Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "ForgotPasswordConfirmation.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T14:46:25.993Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "Login.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Login.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\Login.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Login.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\Login.css", "ViewState": "AgIAAFUAAAAAAAAAAAAAAGMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T14:48:02.761Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "ForgotPassword.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPassword.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\ForgotPassword.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPassword.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\ForgotPassword.css", "ViewState": "AgIAADwAAAAAAAAAAAAAAEYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T14:26:12.383Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "Login.js", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Login.js", "RelativeDocumentMoniker": "PL\\wwwroot\\js\\Auth\\Login.js", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Login.js", "RelativeToolTip": "PL\\wwwroot\\js\\Auth\\Login.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-23T14:49:04.114Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "20250823142050_notification1.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250823142050_notification1.cs", "RelativeDocumentMoniker": "Repository\\Data\\Migrations\\20250823142050_notification1.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250823142050_notification1.cs", "RelativeToolTip": "Repository\\Data\\Migrations\\20250823142050_notification1.cs", "ViewState": "AgIAABAAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T14:20:50.511Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "SpecificationProvider.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\SpecificationProvider.cs", "RelativeDocumentMoniker": "Repository\\SpecificationProvider.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\SpecificationProvider.cs", "RelativeToolTip": "Repository\\SpecificationProvider.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:59:48.003Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "IGenericRepository.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Repository.Contract\\IGenericRepository.cs", "RelativeDocumentMoniker": "Core\\Repository.Contract\\IGenericRepository.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Repository.Contract\\IGenericRepository.cs", "RelativeToolTip": "Core\\Repository.Contract\\IGenericRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:55:45.825Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "Specification.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\Specification.cs", "RelativeDocumentMoniker": "Core\\Specifications\\Specification.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\Specification.cs", "RelativeToolTip": "Core\\Specifications\\Specification.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T14:04:49.053Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "GetByUserId.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\NotificationSpec\\GetByUserId.cs", "RelativeDocumentMoniker": "Core\\Specifications\\NotificationSpec\\GetByUserId.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\NotificationSpec\\GetByUserId.cs", "RelativeToolTip": "Core\\Specifications\\NotificationSpec\\GetByUserId.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAABkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T14:04:51.954Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "AuthController.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "PL\\Controllers\\AuthController.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\AuthController.cs", "RelativeToolTip": "PL\\Controllers\\AuthController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGMAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:33:56.568Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "20250823141748_notification.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\20250823141748_notification.cs", "RelativeDocumentMoniker": "Repository\\Data\\20250823141748_notification.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\20250823141748_notification.cs", "RelativeToolTip": "Repository\\Data\\20250823141748_notification.cs", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T14:17:48.813Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "AppDbContext.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\AppDbContext.cs", "RelativeDocumentMoniker": "Repository\\Data\\AppDbContext.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\AppDbContext.cs", "RelativeToolTip": "Repository\\Data\\AppDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T14:20:15.123Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "GenericRepository.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Repositories\\GenericRepository.cs", "RelativeDocumentMoniker": "Repository\\Repositories\\GenericRepository.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Repositories\\GenericRepository.cs", "RelativeToolTip": "Repository\\Repositories\\GenericRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:55:21.684Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "Notification.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\Notification.cs", "RelativeDocumentMoniker": "Core\\Entities\\Notification.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\Notification.cs", "RelativeToolTip": "Core\\Entities\\Notification.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAgAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:39:02.303Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "NotificationController.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\NotificationController.cs", "RelativeDocumentMoniker": "PL\\Controllers\\NotificationController.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\NotificationController.cs", "RelativeToolTip": "PL\\Controllers\\NotificationController.cs", "ViewState": "AgIAAGwAAAAAAAAAAAAIwLIAAACPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:34:54.176Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "Delete.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Delete.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Delete.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Delete.cshtml", "RelativeToolTip": "PL\\Views\\User\\Delete.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T13:11:37.355Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "ApplicationServicesExtentions.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Extentions\\ApplicationServicesExtentions.cs", "RelativeDocumentMoniker": "PL\\Extentions\\ApplicationServicesExtentions.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Extentions\\ApplicationServicesExtentions.cs", "RelativeToolTip": "PL\\Extentions\\ApplicationServicesExtentions.cs", "ViewState": "AgIAACMAAAAAAAAAAAAowFgAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:34:23.286Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "NotificationViewModel.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Models\\NotificationViewModel\\NotificationViewModel.cs", "RelativeDocumentMoniker": "PL\\Models\\NotificationViewModel\\NotificationViewModel.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Models\\NotificationViewModel\\NotificationViewModel.cs", "RelativeToolTip": "PL\\Models\\NotificationViewModel\\NotificationViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:32:55.862Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "UserController.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\UserController.cs", "RelativeDocumentMoniker": "PL\\Controllers\\UserController.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\UserController.cs", "RelativeToolTip": "PL\\Controllers\\UserController.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAwwCIAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:16:08.564Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "DepartmentUsers.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\DepartmentUsers.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\DepartmentUsers.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\DepartmentUsers.cshtml", "RelativeToolTip": "PL\\Views\\User\\DepartmentUsers.cshtml", "ViewState": "AgIAANQAAAAAAAAAAAAAAFIAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T13:17:30.243Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "Index.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Notification\\Index.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Notification\\Index.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Notification\\Index.cshtml", "RelativeToolTip": "PL\\Views\\Notification\\Index.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T13:31:49.543Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "EditProfile.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\EditProfile.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\EditProfile.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\EditProfile.cshtml", "RelativeToolTip": "PL\\Views\\User\\EditProfile.cshtml", "ViewState": "AgIAADYAAAAAAAAAAAAAAD4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T13:13:09.219Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "DbPreProcessExtention.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Extentions\\DbPreProcessExtention.cs", "RelativeDocumentMoniker": "PL\\Extentions\\DbPreProcessExtention.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Extentions\\DbPreProcessExtention.cs", "RelativeToolTip": "PL\\Extentions\\DbPreProcessExtention.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAowBsAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:31:40.687Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "IdentitySeeder.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\DataSeeding\\IdentitySeeder.cs", "RelativeDocumentMoniker": "Repository\\Data\\DataSeeding\\IdentitySeeder.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\DataSeeding\\IdentitySeeder.cs", "RelativeToolTip": "Repository\\Data\\DataSeeding\\IdentitySeeder.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAACEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:30:22.949Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "Program.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Program.cs", "RelativeDocumentMoniker": "PL\\Program.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Program.cs", "RelativeToolTip": "PL\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABsAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:25:51.967Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "Profile.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Profile.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Profile.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Profile.cshtml", "RelativeToolTip": "PL\\Views\\User\\Profile.cshtml", "ViewState": "AgIAAEkAAAAAAAAAAAAAAFgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:14:53.79Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "Index.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Index.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Index.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Index.cshtml", "RelativeToolTip": "PL\\Views\\User\\Index.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:14:24.114Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "Edit.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Edit.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Edit.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Edit.cshtml", "RelativeToolTip": "PL\\Views\\User\\Edit.cshtml", "ViewState": "AgIAABUAAAAAAAAAAAAAADwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:13:58.874Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "Details.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Details.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Details.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Details.cshtml", "RelativeToolTip": "PL\\Views\\User\\Details.cshtml", "ViewState": "AgIAAEsAAAAAAAAAAAAAAFoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:13:39.256Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "Create.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Create.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Create.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Create.cshtml", "RelativeToolTip": "PL\\Views\\User\\Create.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:13:16.623Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "ChangePassword.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\ChangePassword.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\ChangePassword.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\ChangePassword.cshtml", "RelativeToolTip": "PL\\Views\\User\\ChangePassword.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:12:50.867Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "_DepartmentUserRow.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\_DepartmentUserRow.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\_DepartmentUserRow.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\_DepartmentUserRow.cshtml", "RelativeToolTip": "PL\\Views\\User\\_DepartmentUserRow.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:12:48.717Z"}]}, {"DockedWidth": 200, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}]}]}]}