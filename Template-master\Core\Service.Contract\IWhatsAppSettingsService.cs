using Core.Entities;

namespace Core.Service.Contract
{
    public interface IWhatsAppSettingsService
    {
        // Settings management
        Task<IEnumerable<WhatsAppSettings>> GetAllSettingsAsync();
        Task<WhatsAppSettings?> GetSettingByKeyAsync(string key);
        Task<string?> GetSettingValueAsync(string key);
        Task<T?> GetSettingValueAsync<T>(string key);
        Task<bool> GetBooleanSettingAsync(string key, bool defaultValue = false);
        Task<int> GetIntegerSettingAsync(string key, int defaultValue = 0);
        Task<bool> SetSettingAsync(string key, string value, string? updatedByUserId = null, string? description = null);
        Task<bool> SetSettingAsync<T>(string key, T value, string? updatedByUserId = null, string? description = null);
        Task<bool> DeleteSettingAsync(string key);
        Task<bool> ToggleSettingStatusAsync(string key);

        // Predefined settings
        Task<bool> IsAutoReplyEnabledAsync();
        Task<bool> IsNotificationsEnabledAsync();
        Task<bool> IsChatSavingEnabledAsync();
        Task<bool> IsDarkModeEnabledAsync();
        Task<bool> SetAutoReplyEnabledAsync(bool enabled, string? updatedByUserId = null);
        Task<bool> SetNotificationsEnabledAsync(bool enabled, string? updatedByUserId = null);
        Task<bool> SetChatSavingEnabledAsync(bool enabled, string? updatedByUserId = null);
        Task<bool> SetDarkModeEnabledAsync(bool enabled, string? updatedByUserId = null);

        // Settings initialization
        Task InitializeDefaultSettingsAsync();
    }
}
