using Core.Entities;

namespace Core.Specifications.ChatSpec
{
    public class GetAllChats : Specification<Chat>
    {
        public GetAllChats() : base()
        {
            includes.Add(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1));
            includes.Add(c => c.AssignedUser);
            OrderByDesc = c => c.LastMessageAt;
        }

        public GetAllChats(bool includeArchived) : base(c => includeArchived || !c.IsArchived)
        {
            includes.Add(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1));
            includes.Add(c => c.AssignedUser);
            OrderByDesc = c => c.LastMessageAt;
        }

        public GetAllChats(string status) : base(c => c.Status == status)
        {
            includes.Add(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1));
            includes.Add(c => c.AssignedUser);
            OrderByDesc = c => c.LastMessageAt;
        }

        public GetAllChats(string status, bool includeArchived) : base(c => c.Status == status && (includeArchived || !c.IsArchived))
        {
            includes.Add(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1));
            includes.Add(c => c.AssignedUser);
            OrderByDesc = c => c.LastMessageAt;
        }
    }
}
