﻿@model RegisterViewModel
@{
    Layout = "~/Views/Shared/_AuthLayout.cshtml";
}
<link rel="stylesheet" href="~/css/Auth/Register.css">
<div class="register-container">
    <div class="card shadow-lg border-0 rounded-4">
        <div class="card-body p-5">
            <h2 class="text-center fw-bold mb-4 text-primary fs-1">إنشاء حساب جديد</h2>
            @if (!ViewData.ModelState.IsValid && ViewData.ModelState.ErrorCount > 0)
            {
                <div class="alert alert-danger alert-dismissible fade show fs-5 mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>يرجى تصحيح الأخطاء التالية:</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    <div asp-validation-summary="All" class="mt-2 fs-6"></div>
                </div>
            }

            <form asp-action="Register" method="post">
                <div asp-validation-summary="All" class="d-none" role="alert"></div>

                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="form-group mb-1">
                            <label asp-for="FullName" class="form-label fw-semibold fs-4">الاسم الكامل</label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input asp-for="FullName" class="form-control rounded-end fs-5"
                                       placeholder="أدخل اسمك الكامل" />
                            </div>
                            <span asp-validation-for="FullName" class="text-danger fs-6 mt-1"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group mb-1">
                            <label asp-for="Email" class="form-label fw-semibold fs-4">البريد الإلكتروني</label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input asp-for="Email" class="form-control rounded-end fs-5"
                                       placeholder="أدخل بريدك الإلكتروني" dir="ltr" />
                            </div>
                            <span asp-validation-for="Email" class="text-danger fs-6 mt-1"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group mb-1">
                            <label asp-for="Password" class="form-label fw-semibold fs-4">كلمة المرور</label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input asp-for="Password" type="password" class="form-control fs-5"
                                       id="password" placeholder="أدخل كلمة مرور قوية" dir="ltr" />
                                <button type="button" class="btn btn-outline-secondary"
                                        onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="passwordToggle"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Password" class="text-danger fs-6 mt-1"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group mb-1">
                            <label asp-for="ConfirmPassword" class="form-label fw-semibold fs-4">تأكيد كلمة المرور</label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input asp-for="ConfirmPassword" type="password" class="form-control fs-5"
                                       id="confirmPassword" placeholder="أعد إدخال كلمة المرور" dir="ltr" />
                                <button type="button" class="btn btn-outline-secondary"
                                        onclick="togglePassword('confirmPassword')">
                                    <i class="fas fa-eye" id="confirmPasswordToggle"></i>
                                </button>
                            </div>
                            <span asp-validation-for="ConfirmPassword" class="text-danger fs-6 mt-1"></span>
                        </div>
                    </div>
        

                <div class="mt-5">
                    <button type="submit" class="btn btn-primary w-100 py-3 rounded-3 fw-bold fs-4 shadow-sm">
                        <i class="fas fa-user-plus me-2"></i> إنشاء الحساب
                    </button>
                </div>
            </form>

            <p class="mt-4 text-center fs-5">
                <span class="text-muted">لديك حساب بالفعل؟</span>
                <a asp-controller="Auth" asp-action="Login" class="text-decoration-none fw-semibold text-primary">تسجيل الدخول</a>
            </p>
        </div>
    </div>
</div>
<script src="~/js/Auth/Login.js"></script>
