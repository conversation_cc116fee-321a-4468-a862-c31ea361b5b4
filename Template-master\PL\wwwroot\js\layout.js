$(document).ready(function () {
    // Load notifications
    loadNotifications();

    // Refresh notifications every 60 seconds
    setInterval(loadNotifications, 60000);

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl)
    });

    // Fixed sidebar toggle function for RTL layout
    let sidebarCollapsed = false;
    $('[data-widget="pushmenu"]').on('click', function(e) {
        e.preventDefault();

        sidebarCollapsed = !sidebarCollapsed;
        $('body').toggleClass('sidebar-collapse', sidebarCollapsed);

        if (sidebarCollapsed) {
            // Collapse sidebar - move off screen
            $('.app-sidebar').css({
                'right': '-250px',
                'transition': 'right 0.3s ease'
            });
            $('.content-wrapper, .main-header').css({
                'margin-right': '0',
                'transition': 'margin-right 0.3s ease'
            });
        } else {
            // Expand sidebar - bring back on screen
            $('.app-sidebar').css({
                'right': '0',
                'transition': 'right 0.3s ease'
            });
            $('.content-wrapper, .main-header').css({
                'margin-right': '250px',
                'transition': 'margin-right 0.3s ease'
            });
        }
    });

    // Fullscreen toggle with cross-browser support
    $('[data-widget="fullscreen"]').on('click', function () {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.warn('Error attempting to enable fullscreen:', err);
            });
        } else if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    });

    // Dark Mode Toggle
    function applyDarkMode(enabled) {
        if (enabled) {
            $('body').addClass('dark-mode');
            $('#darkModeToggle i').removeClass('fa-moon').addClass('fa-sun');
        } else {
            $('body').removeClass('dark-mode');
            $('#darkModeToggle i').removeClass('fa-sun').addClass('fa-moon');
        }
    }

    let darkMode = localStorage.getItem('darkMode') === 'enabled';
    applyDarkMode(darkMode);

    $('#darkModeToggle').on('click', function () {
        darkMode = !darkMode;
        localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled');
        applyDarkMode(darkMode);
    });

    // Bootstrap Dropdown Initialization
    var dropdowns = [].slice.call(document.querySelectorAll('.dropdown-toggle'))
    dropdowns.map(function (dropdownToggle) {
        return new bootstrap.Dropdown(dropdownToggle, {
            autoClose: true
        });
    });
});

function loadNotifications() {
    // Call the fetchUnreadNotifications function from Notification.js
    fetchUnreadNotifications();
}

document.addEventListener('DOMContentLoaded', function () {
    const el = document.querySelector(".main-header");
    if (el) {
        el.style.marginRight = "250px";
    }
});
