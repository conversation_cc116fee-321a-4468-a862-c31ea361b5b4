$(document).ready(function () {
    // تبديل أيقونة زر الطي عند النقر
    $('.collapse-btn').on('click', function() {
        $(this).find('i').toggleClass('fa-minus fa-plus');
    });

    // تهيئة التلميحات
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // إضافة متابعة التمرير الأفقي وإظهار/إخفاء مؤشرات التمرير
    $('.scrollable-table-wrapper').each(function() {
        var $wrapper = $(this);
        var $leftHint = $wrapper.find('.scrollable-hint.left');
        var $rightHint = $wrapper.find('.scrollable-hint.right');

        $wrapper.on('scroll', function() {
            var scrollLeft = $wrapper.scrollLeft();
            var maxScrollLeft = $wrapper[0].scrollWidth - $wrapper.width();

            // تحديث ظهور مؤشرات التمرير
            if (scrollLeft <= 10) {
                $rightHint.fadeIn();
                $leftHint.fadeOut();
            } else if (scrollLeft >= maxScrollLeft - 10) {
                $leftHint.fadeIn();
                $rightHint.fadeOut();
            } else {
                $leftHint.fadeIn();
                $rightHint.fadeIn();
            }
        });

        // التحقق مما إذا كان التمرير مطلوبًا
        setTimeout(function() {
            if ($wrapper[0].scrollWidth > $wrapper.width()) {
                $rightHint.fadeIn();
            } else {
                $leftHint.hide();
                $rightHint.hide();
            }
        }, 500);
    });

    // تهيئة جدول سجلات التغيير
    $('#changeLogsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: getChangeLogsUrl, // This will be set from the view
            type: 'POST'
        },
        language: {
            url: "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
        },
        columns: [
            { data: 'entityName', className: "text-center" },
            { data: 'entityId', className: "text-center" },
            { data: 'actionType', className: "text-center" },
            {
                data: 'oldValues',
                className: "text-center",
                render: function (data) {
                    return formatJsonButton(data, 'القيم القديمة');
                }
            },
            {
                data: 'newValues',
                className: "text-center",
                render: function (data) {
                    return formatJsonButton(data, 'القيم الجديدة');
                }
            },
            { data: 'changeDate', className: "text-center" },
            { data: 'userFullName', className: "text-center" }
        ]
    });

    // التعامل مع أيقونة الإشعارات
    $('#notificationIcon').on('click', function(e) {
        e.preventDefault();
        $(this).next('.dropdown-menu').toggleClass('show');
    });

    // التأكد من عمل القائمة المنسدلة للملف الشخصي
    $('#navbarDropdown').on('click', function(e) {
        e.preventDefault();
        $(this).next('.dropdown-menu').toggleClass('show');
    });

    // إغلاق القوائم المنسدلة عند النقر خارجها
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.dropdown').length) {
            $('.dropdown-menu').removeClass('show');
        }
    });

    // إضافة تأثيرات حركية للبطاقات
    $('.stat-card').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
        $(this).addClass('animate__fadeInUp');
    });
});

function formatJsonButton(json, title) {
    if (!json) return '<span class="text-muted">لا توجد بيانات</span>';
    try {
        return `<button class="action-btn btn btn-sm btn-outline-info" onclick='showJsonModal(${JSON.stringify(json)}, "${title}")'>
                    <i class="fas fa-eye me-1"></i> عرض
                </button>`;
    } catch (error) {
        return '<span class="text-danger">بيانات غير صالحة</span>';
    }
}

function showJsonModal(jsonString, title) {
    try {
        let parsedData = JSON.parse(jsonString.replace(/&quot;/g, '"'));
        document.getElementById("jsonData").innerText = JSON.stringify(parsedData, null, 4);
        let modal = new bootstrap.Modal(document.getElementById("jsonModal"));
        modal.show();
    } catch (error) {
        console.error("خطأ في تحليل JSON:", error);
        alert("تنسيق JSON غير صالح!");
    }
}
