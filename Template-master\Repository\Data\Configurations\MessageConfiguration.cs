using Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Repository.Data.Configurations
{
    public class MessageConfiguration : IEntityTypeConfiguration<Message>
    {
        public void Configure(EntityTypeBuilder<Message> builder)
        {
            builder.HasKey(m => m.Id);

            builder.Property(m => m.ChatId)
                .IsRequired();

            builder.Property(m => m.Content)
                .IsRequired()
                .HasMaxLength(2000);

            builder.Property(m => m.IsSent)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(m => m.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");

            builder.Property(m => m.IsRead)
                .HasDefaultValue(false);

            builder.Property(m => m.IsDelivered)
                .HasDefaultValue(false);

            builder.Property(m => m.MessageType)
                .IsRequired()
                .HasMaxLength(20)
                .HasDefaultValue("text");

            builder.Property(m => m.AttachmentUrl)
                .HasMaxLength(500);

            builder.Property(m => m.AttachmentName)
                .HasMaxLength(100);

    
            // Relationships
            builder.HasOne(m => m.Chat)
                .WithMany(c => c.Messages)
                .HasForeignKey(m => m.ChatId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(m => m.SentByUser)
                .WithMany()
                .HasForeignKey(m => m.SentByUserId)
                .OnDelete(DeleteBehavior.SetNull);


            // Indexes
            builder.HasIndex(m => m.ChatId);

            builder.HasIndex(m => m.CreatedAt);

            builder.HasIndex(m => m.IsSent);

            builder.HasIndex(m => m.IsRead);

        

            builder.HasIndex(m => m.SentByUserId);

            builder.HasIndex(m => new { m.ChatId, m.CreatedAt });

            builder.HasIndex(m => new { m.ChatId, m.IsRead });
        }
    }
}
