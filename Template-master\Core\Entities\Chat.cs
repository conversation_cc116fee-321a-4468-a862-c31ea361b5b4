using System.ComponentModel.DataAnnotations;

namespace Core.Entities
{
    public class Chat : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string CustomerName { get; set; } = string.Empty;

        [Required]
        [MaxLength(20)]
        public string CustomerPhone { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? CustomerEmail { get; set; }

        [MaxLength(2)]
        public string CustomerAvatar { get; set; } = string.Empty;

        [MaxLength(20)]
        public string Status { get; set; } = "active"; // active, inactive, pending

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime LastMessageAt { get; set; } = DateTime.UtcNow;

        [MaxLength(500)]
        public string? LastMessage { get; set; }

        public int UnreadCount { get; set; } = 0;

        public bool IsArchived { get; set; } = false;

        // Navigation properties
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();

        // Service customer properties
        public DateTime? JoinDate { get; set; }

        public DateTime? LastContactDate { get; set; }

        public int TotalTickets { get; set; } = 0;

        public int ResolvedTickets { get; set; } = 0;

        [MaxLength(500)]
        public string? Notes { get; set; }

        // Assigned user for customer service
        public string? AssignedUserId { get; set; }
        public virtual User? AssignedUser { get; set; }
    }
}
