using System.ComponentModel.DataAnnotations;

namespace Core.Entities
{
    public class Chat : BaseEntity
    {
        public int CustomerId { get; set; }
        public virtual Customer Customer { get; set; }

        public DateTime LastMessageAt { get; set; } = DateTime.UtcNow;

        [MaxLength(500)]
        public string? LastMessage { get; set; }

        public int UnreadCount { get; set; } = 0;

        // Navigation properties
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();

        [MaxLength(500)]
        public string? Notes { get; set; }

        // Assigned user for customer service
        public string? AssignedUserId { get; set; }
        public virtual User? AssignedUser { get; set; }
    }
}
