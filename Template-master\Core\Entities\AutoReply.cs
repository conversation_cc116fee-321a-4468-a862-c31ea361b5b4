using System.ComponentModel.DataAnnotations;

namespace Core.Entities
{
    public class AutoReply : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Trigger { get; set; } = string.Empty;

        [Required]
        [<PERSON><PERSON>ength(2000)]
        public string Response { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        [MaxLength(20)]
        public string MatchType { get; set; } = "contains"; // contains, exact, starts_with, ends_with

        public bool IsCaseSensitive { get; set; } = false;

        public int Priority { get; set; } = 1; // Higher number = higher priority

        // Usage statistics
        public int UsageCount { get; set; } = 0;

        public DateTime? LastUsedAt { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        // Created by user
        public string? CreatedByUserId { get; set; }
        public virtual User? CreatedByUser { get; set; }

        // Navigation properties
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
    }
}
