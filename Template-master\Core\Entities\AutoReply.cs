using System.ComponentModel.DataAnnotations;

namespace Core.Entities
{
    public class AutoReply : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Trigger { get; set; } = string.Empty;

        [Required]
        [<PERSON><PERSON>eng<PERSON>(2000)]
        public string Response { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public int Priority { get; set; } = 1; // Higher number = higher priority

        public string? CreatedByUserId { get; set; }
        public virtual User? CreatedByUser { get; set; }

    }
}
