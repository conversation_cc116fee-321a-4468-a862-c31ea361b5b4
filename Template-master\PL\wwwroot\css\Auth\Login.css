﻿
.login-container {
    max-width: 600px; /* زيادة العرض قليلاً */
    width: 100%;
    margin: 0 auto;
}

.card {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    overflow: hidden;
}

.form-control {
    border-radius: 8px;
    padding: 15px; /* زيادة المساحة الداخلية */
    height: auto;
    font-size: 18px; /* زيادة حجم الخط */
    transition: all 0.3s;
}

    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

.input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    font-size: 18px; /* زيادة حجم الأيقونات */
    padding: 0 15px; /* زيادة المساحة الداخلية */
}

.btn-primary {
    background: linear-gradient(45deg, #2937f0, #9f1ae2);
    border: none;
    border-radius: 8px;
    font-size: 20px; /* زيادة حجم الخط للزر */
    transition: all 0.3s;
}

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
        background: linear-gradient(45deg, #1e2bd1, #8614c0);
    }

/* For RTL support with input fields */
input[dir="ltr"] {
    text-align: left;
}

/* Animation for form elements */
.card {
    animation: fadeIn 0.8s ease-in-out;
}

@@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* For error messages */
.text-danger {
    font-size: 16px; /* زيادة حجم رسائل الخطأ */
    margin-top: 5px;
    display: block;
}

/* Make validation summary look better */
.validation-summary-errors ul {
    list-style: none;
    padding-right: 0; /* تعديل للغة العربية */
    margin-bottom: 0;
    font-size: 16px; /* زيادة حجم الخط */
}

/* زيادة حجم الخط في جميع أنحاء التطبيق */
body {
    font-size: 18px;
}

/* تكبير حجم مربع الاختيار "تذكرني" */
.form-check-input {
    width: 20px;
    height: 20px;
}

.form-check-label {
    margin-right: 10px; /* مساحة أكبر للغة العربية */
}

