using Core.Helper;
using Microsoft.AspNetCore.Builder;
using Template.Extentions;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();
builder.Services.AddApplicationServices(builder.Configuration);
builder.Services.Configure<WhatsAppCloudeSettings>(builder.Configuration.GetSection(nameof(WhatsAppCloudeSettings)));

var app = builder.Build();

app.DbPreProcess();

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

//app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseCors(op =>
{
    op.AllowAnyOrigin()
      .AllowAnyMethod()
      .AllowAnyHeader();
});

app.UseAuthorization();

// If you have authentication, put app.UseAuthentication() before UseAuthorization()

app.MapOpenApi();

app.MapControllers();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Auth}/{action=Login}/{id?}");

app.Run();