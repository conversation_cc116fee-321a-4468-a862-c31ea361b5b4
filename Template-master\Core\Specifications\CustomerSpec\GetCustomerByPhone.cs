﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Specifications.CustomerSpec
{
    public class GetCustomerByPhone : Specification<Customer>
    {
        public GetCustomerByPhone(string number): base(c=>c.Number==number)
        {
            includes.Add(c => c.Chat);
            
        }
    }
}
