using Core.Entities;


namespace Core.Service.Contract
{
    public interface IUserService
    {
        Task<bool> UpdateUserProfileAsync(User user);
        Task<bool> ChangePasswordAsync(string userId, string currentPassword, string newPassword);
        Task<bool> InitiatePasswordResetAsync(string email);
        Task<bool> ResetPasswordAsync(string email, string token, string newPassword);

    }
}
