using Core.Entities;

namespace Core.Specifications.MessageSpec
{
    public class GetMessagesByChatId : Specification<Message>
    {
        public GetMessagesByChatId(int chatId) : base(m => m.ChatId == chatId)
        {
            includes.Add(m => m.Chat);
            includes.Add(m => m.SentByUser);
            includes.Add(m => m.AutoReply);
            OrderBy = m => m.CreatedAt;
        }

        public GetMessagesByChatId(int chatId, int skip, int take) : base(m => m.ChatId == chatId)
        {
            includes.Add(m => m.Chat);
            includes.Add(m => m.SentByUser);
            includes.Add(m => m.AutoReply);
            OrderBy = m => m.CreatedAt;
            ApplyPagination(skip, take);
        }

        public GetMessagesByChatId(int chatId, DateTime fromDate) : base(m => m.ChatId == chatId && m.CreatedAt >= fromDate)
        {
            includes.Add(m => m.Chat);
            includes.Add(m => m.SentByUser);
            includes.Add(m => m.AutoReply);
            OrderBy = m => m.CreatedAt;
        }
    }
}
