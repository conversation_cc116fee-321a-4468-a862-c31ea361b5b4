using Core.Entities;

namespace Core.Specifications.AutoReplySpec
{
    public class FindMatchingAutoReply : Specification<AutoReply>
    {
        public FindMatchingAutoReply(string messageContent) : base(ar => 
            ar.IsActive && 
            (ar.MatchType == "contains" && messageContent.ToLower().Contains(ar.Trigger.ToLower()) ||
             ar.MatchType == "exact" && messageContent.ToLower() == ar.Trigger.ToLower() ||
             ar.MatchType == "starts_with" && messageContent.ToLower().StartsWith(ar.Trigger.ToLower()) ||
             ar.MatchType == "ends_with" && messageContent.ToLower().EndsWith(ar.Trigger.ToLower())))
        {
            includes.Add(ar => ar.CreatedByUser);
            OrderByDesc = ar => ar.Priority;
        }

        public FindMatchingAutoReply(string messageContent, bool caseSensitive) : base(ar => 
            ar.IsActive && ar.IsCaseSensitive == caseSensitive &&
            (ar.MatchType == "contains" && 
                (caseSensitive ? messageContent.Contains(ar.Trigger) : messageContent.ToLower().Contains(ar.Trigger.ToLower())) ||
             ar.MatchType == "exact" && 
                (caseSensitive ? messageContent == ar.Trigger : messageContent.ToLower() == ar.Trigger.ToLower()) ||
             ar.MatchType == "starts_with" && 
                (caseSensitive ? messageContent.StartsWith(ar.Trigger) : messageContent.ToLower().StartsWith(ar.Trigger.ToLower())) ||
             ar.MatchType == "ends_with" && 
                (caseSensitive ? messageContent.EndsWith(ar.Trigger) : messageContent.ToLower().EndsWith(ar.Trigger.ToLower()))))
        {
            includes.Add(ar => ar.CreatedByUser);
            OrderByDesc = ar => ar.Priority;
        }
    }
}
