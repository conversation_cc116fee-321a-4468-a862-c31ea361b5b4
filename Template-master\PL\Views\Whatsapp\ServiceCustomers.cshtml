@{
    ViewData["Title"] = "Service Customers";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@Html.AntiForgeryToken()

<style>
    .service-customers-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .customers-header {
        background: #00a884;
        color: white;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .customers-header h4 {
        margin: 0;
        font-weight: 600;
    }

    .header-actions {
        display: flex;
        gap: 10px;
    }

    .header-actions button {
        background: rgba(255,255,255,0.2);
        border: none;
        color: white;
        padding: 8px 15px;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .header-actions button:hover {
        background: rgba(255,255,255,0.3);
    }

    .customers-search {
        padding: 20px;
        border-bottom: 1px solid #e9ecef;
    }

    .customers-search input {
        width: 100%;
        padding: 12px 20px;
        border: 1px solid #ddd;
        border-radius: 25px;
        outline: none;
        font-size: 14px;
    }

    .customers-list {
        max-height: 600px;
        overflow-y: auto;
    }

    .customer-item {
        padding: 15px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: background-color 0.2s;
        cursor: pointer;
    }

    .customer-item:hover {
        background: #f8f9fa;
    }

    .customer-info {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .customer-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #00a884;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-left: 15px;
        font-size: 18px;
    }

    .customer-details {
        flex: 1;
    }

    .customer-name {
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }

    .customer-phone {
        color: #666;
        font-size: 14px;
        margin-bottom: 3px;
    }

    .customer-status {
        font-size: 12px;
        padding: 3px 8px;
        border-radius: 12px;
        display: inline-block;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .status-pending {
        background: #fff3cd;
        color: #856404;
    }

    .customer-actions {
        display: flex;
        gap: 5px;
    }

    .customer-actions button {
        background: none;
        border: none;
        color: #666;
        font-size: 16px;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        transition: all 0.2s;
    }

    .customer-actions button:hover {
        background: #f0f0f0;
        color: #333;
    }

    .customer-actions .btn-chat {
        color: #00a884;
    }

    .customer-actions .btn-chat:hover {
        background: #e8f5e8;
    }

    .customer-actions .btn-edit {
        color: #007bff;
    }

    .customer-actions .btn-edit:hover {
        background: #e3f2fd;
    }

    .customer-actions .btn-delete {
        color: #dc3545;
    }

    .customer-actions .btn-delete:hover {
        background: #f8d7da;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }

    .stat-card .stat-icon {
        font-size: 40px;
        margin-bottom: 10px;
    }

    .stat-card .stat-number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stat-card .stat-label {
        color: #666;
        font-size: 14px;
    }

    .stat-card.total {
        border-top: 4px solid #007bff;
    }

    .stat-card.total .stat-icon {
        color: #007bff;
    }

    .stat-card.active {
        border-top: 4px solid #28a745;
    }

    .stat-card.active .stat-icon {
        color: #28a745;
    }

    .stat-card.pending {
        border-top: 4px solid #ffc107;
    }

    .stat-card.pending .stat-icon {
        color: #ffc107;
    }

    .stat-card.inactive {
        border-top: 4px solid #dc3545;
    }

    .stat-card.inactive .stat-icon {
        color: #dc3545;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .empty-state i {
        font-size: 80px;
        margin-bottom: 20px;
        color: #ddd;
    }

  /*   media (max-width: 768px) {
        .stats-cards {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .customer-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        
        .customer-actions {
            align-self: flex-end;
        }
    } */
</style>



<section class="content">
    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="stats-cards">
            <div class="stat-card total">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number" id="totalCustomers">0</div>
                <div class="stat-label">إجمالي العملاء</div>
            </div>
            <div class="stat-card active">
                <div class="stat-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-number" id="activeCustomers">0</div>
                <div class="stat-label">عملاء نشطون</div>
            </div>
            <div class="stat-card pending">
                <div class="stat-icon">
                    <i class="fas fa-user-clock"></i>
                </div>
                <div class="stat-number" id="pendingCustomers">0</div>
                <div class="stat-label">في الانتظار</div>
            </div>
            <div class="stat-card inactive">
                <div class="stat-icon">
                    <i class="fas fa-user-times"></i>
                </div>
                <div class="stat-number" id="inactiveCustomers">0</div>
                <div class="stat-label">غير نشطون</div>
            </div>
        </div>

        <!-- Service Customers List -->
        <div class="service-customers-container">
            <div class="customers-header">
                <h4>قائمة عملاء خدمة العملاء</h4>
                <div class="header-actions">
                    <button onclick="addNewCustomer()">
                        <i class="fas fa-plus"></i> إضافة عميل جديد
                    </button>
                    <button onclick="exportCustomers()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <button onclick="goBackToChat()">
                        <i class="fas fa-arrow-right"></i> العودة للمحادثات
                    </button>
                </div>
            </div>
            
            <div class="customers-search">
                <input type="text" placeholder="البحث عن عميل..." id="searchCustomers">
            </div>
            
            <div class="customers-list" id="customersList">
                <!-- Customers will be populated here -->
            </div>
        </div>
    </div>
</section>

<script>
    // Global variables
    let serviceCustomers = [];

    // Load service customers from API
    async function loadServiceCustomers() {
        try {
            showLoading('customersList');
            const response = await fetch('/Whatsapp/GetServiceCustomers');
            const data = await response.json();

            if (data.success) {
                serviceCustomers = data.customers;
                renderCustomersList();
                updateStatistics();
            } else {
                showError('خطأ في تحميل العملاء: ' + data.message);
            }
        } catch (error) {
            console.error('Error loading service customers:', error);
            showError('خطأ في الاتصال بالخادم');
        }
    }

    // Render customers list
    function renderCustomersList() {
        const customersList = document.getElementById('customersList');
        customersList.innerHTML = '';

        if (serviceCustomers.length === 0) {
            customersList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h5>لا توجد عملاء</h5>
                    <p>لم يتم إضافة أي عملاء بعد</p>
                </div>
            `;
            return;
        }

        serviceCustomers.forEach(customer => {
            const customerItem = document.createElement('div');
            customerItem.className = 'customer-item';

            const statusClass = `status-${customer.status}`;
            const statusText = getStatusText(customer.status);

            customerItem.innerHTML = `
                <div class="customer-info">
                    <div class="customer-avatar">${customer.avatar}</div>
                    <div class="customer-details">
                        <div class="customer-name">${customer.name}</div>
                        <div class="customer-phone">${customer.phone}</div>
                        <span class="customer-status ${statusClass}">${statusText}</span>
                    </div>
                </div>
                <div class="customer-actions">
                    <button class="btn-chat" onclick="startChat(${customer.id})" title="بدء محادثة">
                        <i class="fab fa-whatsapp"></i>
                    </button>
                    <button class="btn-edit" onclick="editCustomer(${customer.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-delete" onclick="deleteCustomer(${customer.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            customersList.appendChild(customerItem);
        });
    }

    // Get status text in Arabic
    function getStatusText(status) {
        const statusMap = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'pending': 'في الانتظار'
        };
        return statusMap[status] || status;
    }

    // Update statistics
    function updateStatistics() {
        const total = serviceCustomers.length;
        const active = serviceCustomers.filter(c => c.status === 'active').length;
        const pending = serviceCustomers.filter(c => c.status === 'pending').length;
        const inactive = serviceCustomers.filter(c => c.status === 'inactive').length;
        
        document.getElementById('totalCustomers').textContent = total;
        document.getElementById('activeCustomers').textContent = active;
        document.getElementById('pendingCustomers').textContent = pending;
        document.getElementById('inactiveCustomers').textContent = inactive;
    }

    // Search customers
    function searchCustomers(searchTerm) {
        if (!searchTerm.trim()) {
            renderCustomersList();
            return;
        }

        const filteredCustomers = serviceCustomers.filter(customer =>
            customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            customer.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase()))
        );

        const customersList = document.getElementById('customersList');
        customersList.innerHTML = '';

        if (filteredCustomers.length === 0) {
            customersList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h5>لا توجد نتائج</h5>
                    <p>لم يتم العثور على عملاء مطابقين للبحث</p>
                </div>
            `;
            return;
        }

        filteredCustomers.forEach(customer => {
            const customerItem = document.createElement('div');
            customerItem.className = 'customer-item';

            const statusClass = `status-${customer.status}`;
            const statusText = getStatusText(customer.status);

            customerItem.innerHTML = `
                <div class="customer-info">
                    <div class="customer-avatar">${customer.avatar}</div>
                    <div class="customer-details">
                        <div class="customer-name">${customer.name}</div>
                        <div class="customer-phone">${customer.phone}</div>
                        <span class="customer-status ${statusClass}">${statusText}</span>
                    </div>
                </div>
                <div class="customer-actions">
                    <button class="btn-chat" onclick="startChat(${customer.id})" title="بدء محادثة">
                        <i class="fab fa-whatsapp"></i>
                    </button>
                    <button class="btn-edit" onclick="editCustomer(${customer.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-delete" onclick="deleteCustomer(${customer.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            customersList.appendChild(customerItem);
        });
    }

    // Search input handler
    let searchTimeout;
    document.getElementById('searchCustomers').addEventListener('input', function(e) {
        const searchTerm = e.target.value;

        // Debounce search
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            searchCustomers(searchTerm);
        }, 300);
    });

    // Start chat with customer
    function startChat(customerId) {
        const customer = serviceCustomers.find(c => c.id === customerId);
        if (customer) {
            // Redirect to chat with this customer
            alert(`بدء محادثة مع ${customer.name}`);
            // In a real application, you would redirect to the chat page with this customer
            window.location.href = '@Url.Action("Index", "Whatsapp")';
        }
    }

    // Edit customer
    function editCustomer(customerId) {
        const customer = serviceCustomers.find(c => c.id === customerId);
        if (customer) {
            alert(`تعديل بيانات العميل: ${customer.name}`);
            // In a real application, you would open an edit modal or redirect to edit page
        }
    }

    // Delete customer
    async function deleteCustomer(customerId) {
        const customer = serviceCustomers.find(c => c.id === customerId);
        if (!customer) return;

        if (!confirm(`هل أنت متأكد من حذف العميل: ${customer.name}؟`)) return;

        try {
            const response = await fetch(`/Whatsapp/DeleteChat?id=${customerId}`, {
                method: 'DELETE',
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });

            const data = await response.json();

            if (data.success) {
                await loadServiceCustomers();
                showSuccess('تم حذف العميل بنجاح');
            } else {
                showError('خطأ في حذف العميل: ' + data.message);
            }
        } catch (error) {
            console.error('Error deleting customer:', error);
            showError('خطأ في حذف العميل');
        }
    }

    // Add new customer
    function addNewCustomer() {
        alert('إضافة عميل جديد');
        // In a real application, you would open an add customer modal or redirect to add page
    }

    // Export customers
    function exportCustomers() {
        alert('تصدير قائمة العملاء');
        // In a real application, you would generate and download a CSV/Excel file
    }

    // Go back to chat
    function goBackToChat() {
        window.location.href = '@Url.Action("Index", "Whatsapp")';
    }

    // Utility functions
    function showLoading(elementId) {
        const element = document.getElementById(elementId);
        element.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p>جاري التحميل...</p>
            </div>
        `;
    }

    function showError(message) {
        alert(message);
    }

    function showSuccess(message) {
        alert(message);
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadServiceCustomers();
    });
</script>
