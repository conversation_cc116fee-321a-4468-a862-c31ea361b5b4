﻿
    function togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('password-toggle-icon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
    toggleIcon.classList.remove('fa-eye');
    toggleIcon.classList.add('fa-eye-slash');
        } else {
        passwordInput.type = 'password';
    toggleIcon.classList.remove('fa-eye-slash');
    toggleIcon.classList.add('fa-eye');
        }
    }

    // Show validation summary if it contains errors
    document.addEventListener('DOMContentLoaded', function() {
        const validationSummary = document.querySelector('.validation-summary-errors');
    if (validationSummary) {
        validationSummary.closest('.alert').classList.remove('d-none');
        }
    });
