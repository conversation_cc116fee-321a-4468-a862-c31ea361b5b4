﻿
    function togglePassword(fieldId) {
        var input = document.getElementById(fieldId);
    var icon = document.getElementById(fieldId + 'Icon');

    if (input.type === "password") {
        input.type = "text";
    icon.classList.remove('fa-eye');
    icon.classList.add('fa-eye-slash');
        } else {
        input.type = "password";
    icon.classList.remove('fa-eye-slash');
    icon.classList.add('fa-eye');
        }
    }

    // Password strength checker
    document.addEventListener('DOMContentLoaded', function() {
        const newPassword = document.getElementById('newPassword');
    const lengthCheck = document.getElementById('length');
    const uppercaseCheck = document.getElementById('uppercase');
    const lowercaseCheck = document.getElementById('lowercase');
    const numberCheck = document.getElementById('number');

    newPassword.addEventListener('input', function() {
            const password = this.value;

            // Check length
            if (password.length >= 8) {
        lengthCheck.classList.add('valid-requirement');
    lengthCheck.innerHTML = '<i class="fas fa-check-circle me-2"></i>8 أحرف على الأقل';
            } else {
        lengthCheck.classList.remove('valid-requirement');
    lengthCheck.innerHTML = '<i class="fas fa-circle-notch me-2"></i>8 أحرف على الأقل';
            }

    // Check uppercase
    if (/[A-Z]/.test(password)) {
        uppercaseCheck.classList.add('valid-requirement');
    uppercaseCheck.innerHTML = '<i class="fas fa-check-circle me-2"></i>حرف كبير واحد على الأقل';
            } else {
        uppercaseCheck.classList.remove('valid-requirement');
    uppercaseCheck.innerHTML = '<i class="fas fa-circle-notch me-2"></i>حرف كبير واحد على الأقل';
            }

    // Check lowercase
    if (/[a-z]/.test(password)) {
        lowercaseCheck.classList.add('valid-requirement');
    lowercaseCheck.innerHTML = '<i class="fas fa-check-circle me-2"></i>حرف صغير واحد على الأقل';
            } else {
        lowercaseCheck.classList.remove('valid-requirement');
    lowercaseCheck.innerHTML = '<i class="fas fa-circle-notch me-2"></i>حرف صغير واحد على الأقل';
            }

    // Check number
    if (/[0-9]/.test(password)) {
        numberCheck.classList.add('valid-requirement');
    numberCheck.innerHTML = '<i class="fas fa-check-circle me-2"></i>رقم واحد على الأقل';
            } else {
        numberCheck.classList.remove('valid-requirement');
    numberCheck.innerHTML = '<i class="fas fa-circle-notch me-2"></i>رقم واحد على الأقل';
            }
        });
    });
