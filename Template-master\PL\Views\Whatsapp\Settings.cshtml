@{
    ViewData["Title"] = "WhatsApp Settings";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@Html.AntiForgeryToken()

<style>
    .settings-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .settings-header {
        background: #00a884;
        color: white;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .settings-header h4 {
        margin: 0;
        font-weight: 600;
    }

    .settings-nav {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .nav-tabs {
        border-bottom: none;
        padding: 0 20px;
    }

    .nav-tabs .nav-link {
        border: none;
        color: #666;
        padding: 15px 20px;
        border-radius: 0;
        background: none;
    }

    .nav-tabs .nav-link.active {
        background: white;
        color: #00a884;
        border-bottom: 3px solid #00a884;
    }

    .settings-content {
        padding: 30px;
    }

    .setting-section {
        margin-bottom: 40px;
    }

    .setting-section h5 {
        color: #333;
        margin-bottom: 20px;
        font-weight: 600;
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 10px;
    }

    .setting-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .setting-item:last-child {
        border-bottom: none;
    }

    .setting-info {
        flex: 1;
    }

    .setting-title {
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }

    .setting-description {
        color: #666;
        font-size: 14px;
    }

    .setting-control {
        margin-right: 20px;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 24px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #00a884;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    .auto-reply-list {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }

    .auto-reply-item {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #e9ecef;
        position: relative;
    }

    .auto-reply-item:last-child {
        margin-bottom: 0;
    }

    .auto-reply-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .auto-reply-trigger {
        font-weight: 600;
        color: #00a884;
        background: #e8f5e8;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
    }

    .auto-reply-actions {
        display: flex;
        gap: 5px;
    }

    .auto-reply-actions button {
        background: none;
        border: none;
        color: #666;
        cursor: pointer;
        padding: 5px;
        border-radius: 3px;
        transition: all 0.2s;
    }

    .auto-reply-actions button:hover {
        background: #f0f0f0;
    }

    .auto-reply-actions .btn-edit:hover {
        color: #007bff;
    }

    .auto-reply-actions .btn-delete:hover {
        color: #dc3545;
    }

    .auto-reply-message {
        color: #333;
        line-height: 1.5;
        background: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        border-right: 3px solid #00a884;
    }

    .add-reply-btn {
        background: #00a884;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        transition: background-color 0.2s;
        width: 100%;
        margin-top: 15px;
    }

    .add-reply-btn:hover {
        background: #008f6f;
    }

    .customer-service-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }

    .service-number-item {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .service-number-info {
        flex: 1;
    }

    .service-number {
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .service-name {
        color: #666;
        font-size: 14px;
    }

    .service-status {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        margin-right: 10px;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 0;
        border-radius: 10px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .modal-header {
        background: #00a884;
        color: white;
        padding: 20px;
        border-radius: 10px 10px 0 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .modal-header h5 {
        margin: 0;
        font-weight: 600;
    }

    .close {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
    }

    .modal-body {
        padding: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 8px;
        outline: none;
        font-size: 14px;
    }

    .form-group textarea {
        resize: vertical;
        min-height: 100px;
    }

    .modal-footer {
        padding: 20px;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.2s;
    }

    .btn-primary {
        background: #00a884;
        color: white;
    }

    .btn-primary:hover {
        background: #008f6f;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
    }

   /*  media (max-width: 768px) {
        .settings-content {
            padding: 20px;
        }
        
        .setting-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        
        .setting-control {
            margin-right: 0;
            align-self: flex-end;
        }
    } */
</style>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">إعدادات WhatsApp</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-left">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Whatsapp")">WhatsApp</a></li>
                    <li class="breadcrumb-item active">الإعدادات</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="settings-container">
            <div class="settings-header">
                <h4>إعدادات WhatsApp</h4>
                <button class="btn btn-light" onclick="goBackToChat()">
                    <i class="fas fa-arrow-right"></i> العودة للمحادثات
                </button>
            </div>
            
            <div class="settings-nav">
                <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                            <i class="fas fa-cog"></i> عام
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="auto-reply-tab" data-bs-toggle="tab" data-bs-target="#auto-reply" type="button" role="tab">
                            <i class="fas fa-robot"></i> الرد التلقائي
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="service-numbers-tab" data-bs-toggle="tab" data-bs-target="#service-numbers" type="button" role="tab">
                            <i class="fas fa-phone"></i> أرقام خدمة العملاء
                        </button>
                    </li>
                </ul>
            </div>
            
            <div class="tab-content" id="settingsTabContent">
                <!-- General Settings -->
                <div class="tab-pane fade show active" id="general" role="tabpanel">
                    <div class="settings-content">
                        <div class="setting-section">
                            <h5>الإعدادات العامة</h5>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-title">تفعيل الإشعارات</div>
                                    <div class="setting-description">استقبال إشعارات الرسائل الجديدة</div>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-title">الرد التلقائي</div>
                                    <div class="setting-description">تفعيل الردود التلقائية للرسائل</div>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="autoReplyToggle" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-title">حفظ المحادثات</div>
                                    <div class="setting-description">حفظ تاريخ المحادثات تلقائياً</div>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-title">الوضع المظلم</div>
                                    <div class="setting-description">تفعيل الوضع المظلم للواجهة</div>
                                </div>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Auto Reply Settings -->
                <div class="tab-pane fade" id="auto-reply" role="tabpanel">
                    <div class="settings-content">
                        <div class="setting-section">
                            <h5>إعدادات الرد التلقائي</h5>
                            
                            <div class="auto-reply-list" id="autoReplyList">
                                <!-- Auto reply items will be populated here -->
                            </div>
                            
                            <button class="add-reply-btn" onclick="showAddReplyModal()">
                                <i class="fas fa-plus"></i> إضافة رد تلقائي جديد
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Service Numbers -->
                <div class="tab-pane fade" id="service-numbers" role="tabpanel">
                    <div class="settings-content">
                        <div class="setting-section">
                            <h5>أرقام خدمة العملاء</h5>
                            
                            <div class="customer-service-section" id="serviceNumbersList">
                                <!-- Service numbers will be populated here -->
                            </div>
                            
                            <button class="add-reply-btn" onclick="showAddServiceNumberModal()">
                                <i class="fas fa-plus"></i> إضافة رقم خدمة عملاء جديد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Auto Reply Modal -->
<div id="autoReplyModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h5 id="autoReplyModalTitle">إضافة رد تلقائي جديد</h5>
            <button type="button" class="close" onclick="closeAutoReplyModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="autoReplyForm">
                <div class="form-group">
                    <label for="replyTrigger">الكلمة المفتاحية</label>
                    <input type="text" id="replyTrigger" placeholder="مثال: مرحبا، السعر، المساعدة">
                </div>
                <div class="form-group">
                    <label for="replyMessage">الرد التلقائي</label>
                    <textarea id="replyMessage" placeholder="اكتب الرد التلقائي هنا..."></textarea>
                </div>
                <div class="form-group">
                    <label for="replyStatus">الحالة</label>
                    <select id="replyStatus">
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeAutoReplyModal()">إلغاء</button>
            <button type="button" class="btn btn-primary" onclick="saveAutoReply()">حفظ</button>
        </div>
    </div>
</div>

<!-- Service Number Modal -->
<div id="serviceNumberModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h5 id="serviceNumberModalTitle">إضافة رقم خدمة عملاء جديد</h5>
            <button type="button" class="close" onclick="closeServiceNumberModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="serviceNumberForm">
                <div class="form-group">
                    <label for="serviceName">اسم الخدمة</label>
                    <input type="text" id="serviceName" placeholder="مثال: الدعم الفني، المبيعات">
                </div>
                <div class="form-group">
                    <label for="servicePhone">رقم الهاتف</label>
                    <input type="text" id="servicePhone" placeholder="+966501234567">
                </div>
                <div class="form-group">
                    <label for="serviceDescription">الوصف</label>
                    <textarea id="serviceDescription" placeholder="وصف الخدمة..."></textarea>
                </div>
                <div class="form-group">
                    <label for="serviceNumberStatus">الحالة</label>
                    <select id="serviceNumberStatus">
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeServiceNumberModal()">إلغاء</button>
            <button type="button" class="btn btn-primary" onclick="saveServiceNumber()">حفظ</button>
        </div>
    </div>
</div>

<script>
    // Global variables
    let autoReplies = [];
    let serviceNumbers = [];
    let settings = {};
    let editingReplyId = null;
    let editingServiceId = null;

    // Load auto replies from API
    async function loadAutoReplies() {
        try {
            showLoading('autoReplyList');
            const response = await fetch('/Whatsapp/GetAutoReplies');
            const data = await response.json();

            if (data.success) {
                autoReplies = data.autoReplies;
                renderAutoReplies();
            } else {
                showError('خطأ في تحميل الردود التلقائية: ' + data.message);
            }
        } catch (error) {
            console.error('Error loading auto replies:', error);
            showError('خطأ في الاتصال بالخادم');
        }
    }

    // Render auto replies list
    function renderAutoReplies() {
        const autoReplyList = document.getElementById('autoReplyList');
        autoReplyList.innerHTML = '';

        if (autoReplies.length === 0) {
            autoReplyList.innerHTML = '<div class="text-center p-3 text-muted">لا توجد ردود تلقائية</div>';
            return;
        }

        autoReplies.forEach(reply => {
            const replyItem = document.createElement('div');
            replyItem.className = 'auto-reply-item';

            replyItem.innerHTML = `
                <div class="auto-reply-header">
                    <span class="auto-reply-trigger">${reply.trigger}</span>
                    <div class="auto-reply-actions">
                        <button class="btn-edit" onclick="editAutoReply(${reply.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-delete" onclick="deleteAutoReply(${reply.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                        <label class="toggle-switch" style="margin-right: 10px;">
                            <input type="checkbox" ${reply.isActive ? 'checked' : ''}
                                   onchange="toggleReplyStatus(${reply.id})">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
                <div class="auto-reply-message">${reply.response}</div>
            `;

            autoReplyList.appendChild(replyItem);
        });
    }

    // Load service numbers from API
    async function loadServiceNumbers() {
        try {
            showLoading('serviceNumbersList');
            const response = await fetch('/Whatsapp/GetServiceNumbers');
            const data = await response.json();

            if (data.success) {
                serviceNumbers = data.serviceNumbers;
                renderServiceNumbers();
            } else {
                showError('خطأ في تحميل أرقام الخدمة: ' + data.message);
            }
        } catch (error) {
            console.error('Error loading service numbers:', error);
            showError('خطأ في الاتصال بالخادم');
        }
    }

    // Render service numbers list
    function renderServiceNumbers() {
        const serviceNumbersList = document.getElementById('serviceNumbersList');
        serviceNumbersList.innerHTML = '';

        if (serviceNumbers.length === 0) {
            serviceNumbersList.innerHTML = '<div class="text-center p-3 text-muted">لا توجد أرقام خدمة</div>';
            return;
        }

        serviceNumbers.forEach(service => {
            const serviceItem = document.createElement('div');
            serviceItem.className = 'service-number-item';

            const statusClass = service.isActive ? 'status-active' : 'status-inactive';
            const statusText = service.isActive ? 'نشط' : 'غير نشط';

            serviceItem.innerHTML = `
                <div class="service-number-info">
                    <div class="service-number">${service.phoneNumber}</div>
                    <div class="service-name">${service.name} - ${service.description || ''}</div>
                </div>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span class="service-status ${statusClass}">${statusText}</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="editServiceNumber(${service.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteServiceNumber(${service.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            serviceNumbersList.appendChild(serviceItem);
        });
    }

    // Auto Reply Modal Functions
    function showAddReplyModal() {
        editingReplyId = null;
        document.getElementById('autoReplyModalTitle').textContent = 'إضافة رد تلقائي جديد';
        document.getElementById('replyTrigger').value = '';
        document.getElementById('replyMessage').value = '';
        document.getElementById('replyStatus').value = 'active';
        document.getElementById('autoReplyModal').style.display = 'block';
    }

    function editAutoReply(id) {
        const reply = autoReplies.find(r => r.id === id);
        if (reply) {
            editingReplyId = id;
            document.getElementById('autoReplyModalTitle').textContent = 'تعديل الرد التلقائي';
            document.getElementById('replyTrigger').value = reply.trigger;
            document.getElementById('replyMessage').value = reply.response;
            document.getElementById('replyStatus').value = reply.isActive ? 'active' : 'inactive';
            document.getElementById('autoReplyModal').style.display = 'block';
        }
    }

    function closeAutoReplyModal() {
        document.getElementById('autoReplyModal').style.display = 'none';
    }

    async function saveAutoReply() {
        const trigger = document.getElementById('replyTrigger').value.trim();
        const message = document.getElementById('replyMessage').value.trim();
        const status = document.getElementById('replyStatus').value;

        if (!trigger || !message) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('id', editingReplyId || 0);
            formData.append('trigger', trigger);
            formData.append('response', message);
            formData.append('isActive', status === 'active');

            const response = await fetch('/Whatsapp/SaveAutoReply', {
                method: 'POST',
                body: formData,
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });

            const data = await response.json();

            if (data.success) {
                await loadAutoReplies();
                closeAutoReplyModal();
                showSuccess('تم حفظ الرد التلقائي بنجاح');
            } else {
                showError('خطأ في حفظ الرد التلقائي: ' + data.message);
            }
        } catch (error) {
            console.error('Error saving auto reply:', error);
            showError('خطأ في حفظ الرد التلقائي');
        }
    }

    async function deleteAutoReply(id) {
        if (!confirm('هل أنت متأكد من حذف هذا الرد التلقائي؟')) return;

        try {
            const response = await fetch(`/Whatsapp/DeleteAutoReply?id=${id}`, {
                method: 'DELETE',
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });

            const data = await response.json();

            if (data.success) {
                await loadAutoReplies();
                showSuccess('تم حذف الرد التلقائي بنجاح');
            } else {
                showError('خطأ في حذف الرد التلقائي: ' + data.message);
            }
        } catch (error) {
            console.error('Error deleting auto reply:', error);
            showError('خطأ في حذف الرد التلقائي');
        }
    }

    async function toggleReplyStatus(id) {
        try {
            const formData = new FormData();
            formData.append('id', id);

            const response = await fetch('/Whatsapp/ToggleAutoReply', {
                method: 'POST',
                body: formData,
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });

            const data = await response.json();

            if (data.success) {
                await loadAutoReplies();
            } else {
                showError('خطأ في تحديث حالة الرد التلقائي: ' + data.message);
            }
        } catch (error) {
            console.error('Error toggling auto reply status:', error);
            showError('خطأ في تحديث حالة الرد التلقائي');
        }
    }

    // Service Number Modal Functions
    function showAddServiceNumberModal() {
        editingServiceId = null;
        document.getElementById('serviceNumberModalTitle').textContent = 'إضافة رقم خدمة عملاء جديد';
        document.getElementById('serviceName').value = '';
        document.getElementById('servicePhone').value = '';
        document.getElementById('serviceDescription').value = '';
        document.getElementById('serviceNumberStatus').value = 'active';
        document.getElementById('serviceNumberModal').style.display = 'block';
    }

    function editServiceNumber(id) {
        const service = serviceNumbers.find(s => s.id === id);
        if (service) {
            editingServiceId = id;
            document.getElementById('serviceNumberModalTitle').textContent = 'تعديل رقم خدمة العملاء';
            document.getElementById('serviceName').value = service.name;
            document.getElementById('servicePhone').value = service.phoneNumber;
            document.getElementById('serviceDescription').value = service.description || '';
            document.getElementById('serviceNumberStatus').value = service.isActive ? 'active' : 'inactive';
            document.getElementById('serviceNumberModal').style.display = 'block';
        }
    }

    function closeServiceNumberModal() {
        document.getElementById('serviceNumberModal').style.display = 'none';
    }

    async function saveServiceNumber() {
        const name = document.getElementById('serviceName').value.trim();
        const phone = document.getElementById('servicePhone').value.trim();
        const description = document.getElementById('serviceDescription').value.trim();
        const status = document.getElementById('serviceNumberStatus').value;

        if (!name || !phone) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('id', editingServiceId || 0);
            formData.append('name', name);
            formData.append('phone', phone);
            formData.append('description', description);
            formData.append('isActive', status === 'active');

            const response = await fetch('/Whatsapp/SaveServiceNumber', {
                method: 'POST',
                body: formData,
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });

            const data = await response.json();

            if (data.success) {
                await loadServiceNumbers();
                closeServiceNumberModal();
                showSuccess('تم حفظ رقم خدمة العملاء بنجاح');
            } else {
                showError('خطأ في حفظ رقم خدمة العملاء: ' + data.message);
            }
        } catch (error) {
            console.error('Error saving service number:', error);
            showError('خطأ في حفظ رقم خدمة العملاء');
        }
    }

    async function deleteServiceNumber(id) {
        if (!confirm('هل أنت متأكد من حذف رقم خدمة العملاء هذا؟')) return;

        try {
            const response = await fetch(`/Whatsapp/DeleteServiceNumber?id=${id}`, {
                method: 'DELETE',
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });

            const data = await response.json();

            if (data.success) {
                await loadServiceNumbers();
                showSuccess('تم حذف رقم خدمة العملاء بنجاح');
            } else {
                showError('خطأ في حذف رقم خدمة العملاء: ' + data.message);
            }
        } catch (error) {
            console.error('Error deleting service number:', error);
            showError('خطأ في حذف رقم خدمة العملاء');
        }
    }

    // Go back to chat
    function goBackToChat() {
        window.location.href = '@Url.Action("Index", "Whatsapp")';
    }

    // Close modals when clicking outside
    window.onclick = function(event) {
        const autoReplyModal = document.getElementById('autoReplyModal');
        const serviceNumberModal = document.getElementById('serviceNumberModal');
        
        if (event.target === autoReplyModal) {
            closeAutoReplyModal();
        }
        if (event.target === serviceNumberModal) {
            closeServiceNumberModal();
        }
    }

    // Utility functions
    function showLoading(elementId) {
        const element = document.getElementById(elementId);
        element.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p>جاري التحميل...</p>
            </div>
        `;
    }

    function showError(message) {
        alert(message);
    }

    function showSuccess(message) {
        alert(message);
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadAutoReplies();
        loadServiceNumbers();
    });
</script>
