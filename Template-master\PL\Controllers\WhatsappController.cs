using Microsoft.AspNetCore.Mvc;
using Core.Service.Contract;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace PL.Controllers
{
    [Authorize]
    public class WhatsappController : Controller
    {
        private readonly ILogger<WhatsappController> _logger;
        private readonly IWhatsAppService _whatsAppService;
        private readonly IAutoReplyService _autoReplyService;
       
        private readonly IWhatsAppSettingsService _settingsService;

        public WhatsappController(
            ILogger<WhatsappController> logger,
            IWhatsAppService whatsAppService,
            IAutoReplyService autoReplyService,
           
            IWhatsAppSettingsService settingsService)
        {
            _logger = logger;
            _whatsAppService = whatsAppService;
            _autoReplyService = autoReplyService;
           
            _settingsService = settingsService;
        }

        /// <summary>
        /// Main WhatsApp chat interface
        /// </summary>
        /// <returns>WhatsApp chat view</returns>
        public async Task<IActionResult> Index()
        {
            try
            {
                _logger.LogInformation("WhatsApp chat page accessed by user: {UserId}", User.FindFirstValue(ClaimTypes.NameIdentifier));

                // Initialize default settings if needed
                await _settingsService.InitializeDefaultSettingsAsync();

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accessing WhatsApp chat page");
                return View("Error");
            }
        }

        /// <summary>
        /// Service customers management page
        /// </summary>
        /// <returns>Service customers view</returns>
        public async Task<IActionResult> ServiceCustomers()
        {
            try
            {
                _logger.LogInformation("Service customers page accessed by user: {UserId}", User.FindFirstValue(ClaimTypes.NameIdentifier));
                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accessing service customers page");
                return View("Error");
            }
        }

        /// <summary>
        /// WhatsApp settings page
        /// </summary>
        /// <returns>Settings view</returns>
        public async Task<IActionResult> Settings()
        {
            try
            {
                _logger.LogInformation("WhatsApp settings page accessed by user: {UserId}", User.FindFirstValue(ClaimTypes.NameIdentifier));
                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accessing WhatsApp settings page");
                return View("Error");
            }
        }

        /// <summary>
        /// API endpoint to get all chats
        /// </summary>
        /// <returns>JSON response with chats</returns>
        [HttpGet]
        public async Task<IActionResult> GetChats()
        {
            try
            {
                var chats = await _whatsAppService.GetAllChatsAsync();
                var chatData = chats.Select(c => new
                {
                    id = c.Id,
                    customerName = c.Customer.Name,
                    customerPhone = c.Customer.Number, 
                    lastMessage = c.LastMessage,
                    lastMessageAt = c.LastMessageAt.ToString("HH:mm"),
                    unreadCount = c.UnreadCount,
                });

                return Json(new { success = true, chats = chatData });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chats");
                return Json(new { success = false, message = "Error getting chats" });
            }
        }

        /// <summary>
        /// API endpoint to get chat messages
        /// </summary>
        /// <param name="chatId">Chat ID</param>
        /// <returns>JSON response with messages</returns>
        [HttpGet]
        public async Task<IActionResult> GetChatMessages(int chatId)
        {
            try
            {
                _logger.LogInformation("Getting messages for chat ID: {ChatId}", chatId);

                var messages = await _whatsAppService.GetMessagesByChatIdAsync(chatId);
                var messageData = messages.Select(m => new
                {
                    id = m.Id,
                    content = m.Content,
                    isSent = m.IsSent,
                    createdAt = m.CreatedAt.ToString("HH:mm"),
                    isRead = m.IsRead,
                    messageType = m.MessageType
                   
                });

                return Json(new { success = true, messages = messageData });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages for chat ID: {ChatId}", chatId);
                return Json(new { success = false, message = "Error getting messages" });
            }
        }

        /// <summary>
        /// API endpoint to send a message
        /// </summary>
        /// <param name="chatId">Chat ID</param>
        /// <param name="message">Message content</param>
        /// <returns>JSON response</returns>
        [HttpPost]
        public async Task<IActionResult> SendMessage(int chatId, string message)
        {
            try
            {
                _logger.LogInformation("Sending message to chat ID: {ChatId}", chatId);

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var sentMessage = await _whatsAppService.SendMessageAsync(chatId, message, userId);

                return Json(new {
                    success = true,
                    message = "Message sent successfully",
                    messageId = sentMessage.Id,
                    timestamp = sentMessage.CreatedAt.ToString("HH:mm")
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message to chat ID: {ChatId}", chatId);
                return Json(new { success = false, message = "Error sending message" });
            }
        }

        /// <summary>
        /// API endpoint to receive a message (simulate incoming message)
        /// </summary>
        /// <param name="chatId">Chat ID</param>
        /// <param name="message">Message content</param>
        /// <returns>JSON response</returns>
        [HttpPost]
        public async Task<IActionResult> ReceiveMessage(int chatId, string message)
        {
            try
            {
                _logger.LogInformation("Receiving message for chat ID: {ChatId}", chatId);

                var receivedMessage = await _whatsAppService.ReceiveMessageAsync(chatId, message);

                return Json(new {
                    success = true,
                    message = "Message received successfully",
                    messageId = receivedMessage.Id,
                    timestamp = receivedMessage.CreatedAt.ToString("HH:mm")
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error receiving message for chat ID: {ChatId}", chatId);
                return Json(new { success = false, message = "Error receiving message" });
            }
        }

        /// <summary>
        /// API endpoint to get service customers
        /// </summary>
        ///// <returns>JSON response with customers</returns>
        //[HttpGet]
        //public async Task<IActionResult> GetServiceCustomers()
        //{
        //    try
        //    {
        //        _logger.LogInformation("Getting service customers");

        //        var chats = await _whatsAppService.GetAllChatsAsync();
        //        var customerData = chats.Select(c => new
        //        {
        //            id = c.Id,
        //            name = c.CustomerName,
        //            phone = c.CustomerPhone,
        //            email = c.CustomerEmail,
        //            avatar = c.CustomerAvatar,
        //            status = c.Status,
        //            joinDate = c.JoinDate?.ToString("yyyy-MM-dd"),
        //            lastContact = c.LastContactDate?.ToString("yyyy-MM-dd"),
        //            totalTickets = c.TotalTickets,
        //            resolvedTickets = c.ResolvedTickets
        //        });

        //        return Json(new { success = true, customers = customerData });
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error getting service customers");
        //        return Json(new { success = false, message = "Error getting service customers" });
        //    }
        //}

        /// <summary>
        /// API endpoint to get auto replies
        /// </summary>
        /// <returns>JSON response with auto replies</returns>
        [HttpGet]
        public async Task<IActionResult> GetAutoReplies()
        {
            try
            {
                _logger.LogInformation("Getting auto replies");

                var autoReplies = await _autoReplyService.GetAllAutoRepliesAsync();
                var replyData = autoReplies.Select(ar => new
                {
                    id = ar.Id,
                    trigger = ar.Trigger,
                    response = ar.Response,
                    isActive = ar.IsActive,
                    priority = ar.Priority,
        
                });

                return Json(new { success = true, autoReplies = replyData });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto replies");
                return Json(new { success = false, message = "Error getting auto replies" });
            }
        }

        /// <summary>
        /// API endpoint to add/update auto reply
        /// </summary>
        /// <param name="id">Auto reply ID (0 for new)</param>
        /// <param name="trigger">Trigger keyword</param>
        /// <param name="response">Auto reply message</param>
        /// <param name="matchType">Match type</param>
        /// <param name="priority">Priority</param>
        /// <param name="isActive">Whether the auto reply is active</param>
        /// <returns>JSON response</returns>
        [HttpPost]
        public async Task<IActionResult> SaveAutoReply(int id, string trigger, string response,
            string matchType = "contains", int priority = 1, bool isActive = true)
        {
            try
            {
                _logger.LogInformation("Saving auto reply for trigger: {Trigger}", trigger);

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                if (id == 0)
                {
                    // Create new auto reply
                    var autoReply = await _autoReplyService.CreateAutoReplyAsync(trigger, response, userId, matchType, false, priority);
                    return Json(new {
                        success = true,
                        message = "Auto reply created successfully",
                        id = autoReply.Id
                    });
                }
                else
                {
                    // Update existing auto reply
                    var success = await _autoReplyService.UpdateAutoReplyAsync(id, trigger, response, matchType, false, priority);
                    return Json(new {
                        success = success,
                        message = success ? "Auto reply updated successfully" : "Auto reply not found"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving auto reply for trigger: {Trigger}", trigger);
                return Json(new { success = false, message = "Error saving auto reply" });
            }
        }

        /// <summary>
        /// API endpoint to delete auto reply
        /// </summary>
        /// <param name="id">Auto reply ID</param>
        /// <returns>JSON response</returns>
        [HttpDelete]
        public async Task<IActionResult> DeleteAutoReply(int id)
        {
            try
            {
                _logger.LogInformation("Deleting auto reply ID: {Id}", id);

                var success = await _autoReplyService.DeleteAutoReplyAsync(id);
                return Json(new {
                    success = success,
                    message = success ? "Auto reply deleted successfully" : "Auto reply not found"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting auto reply ID: {Id}", id);
                return Json(new { success = false, message = "Error deleting auto reply" });
            }
        }

        /// <summary>
        /// API endpoint to toggle auto reply status
        /// </summary>
        /// <param name="id">Auto reply ID</param>
        /// <returns>JSON response</returns>
        [HttpPost]
        public async Task<IActionResult> ToggleAutoReply(int id)
        {
            try
            {
                _logger.LogInformation("Toggling auto reply status for ID: {Id}", id);

                var success = await _autoReplyService.ToggleAutoReplyStatusAsync(id);
                return Json(new {
                    success = success,
                    message = success ? "Auto reply status updated successfully" : "Auto reply not found"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling auto reply status for ID: {Id}", id);
                return Json(new { success = false, message = "Error updating auto reply status" });
            }
        }

        /// <summary>
        /// API endpoint to get service numbers
        /// </summary>
        /// <returns>JSON response with service numbers</returns>
        //[HttpGet]
        //public async Task<IActionResult> GetServiceNumbers()
        //{
        //    try
        //    {
        //        _logger.LogInformation("Getting service numbers");

        //        var serviceNumbers = await _serviceNumberService.GetAllServiceNumbersAsync();
        //        var serviceData = serviceNumbers.Select(sn => new
        //        {
        //            id = sn.Id,
        //            name = sn.Name,
        //            phoneNumber = sn.PhoneNumber,
        //            description = sn.Description,
        //            serviceType = sn.ServiceType,
        //            isActive = sn.IsActive,
        //            displayOrder = sn.DisplayOrder,
        //            contactCount = sn.ContactCount,
        //            createdAt = sn.CreatedAt.ToString("yyyy-MM-dd")
        //        });

        //        return Json(new { success = true, serviceNumbers = serviceData });
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error getting service numbers");
        //        return Json(new { success = false, message = "Error getting service numbers" });
        //    }
        //}

        /// <summary>
        /// API endpoint to add/update service number
        /// </summary>
        /// <param name="id">Service number ID (0 for new)</param>
        /// <param name="name">Service name</param>
        /// <param name="phone">Phone number</param>
        /// <param name="description">Service description</param>
        /// <param name="serviceType">Service type</param>
        /// <param name="isActive">Whether the service is active</param>
        /// <returns>JSON response</returns>
        //[HttpPost]
        //public async Task<IActionResult> SaveServiceNumber(int id, string name, string phone,
        //    string description, string serviceType = "support", bool isActive = true)
        //{
        //    try
        //    {
        //        _logger.LogInformation("Saving service number: {Name} - {Phone}", name, phone);

        //        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

        //        if (id == 0)
        //        {
        //            // Create new service number
        //            var serviceNumber = await _serviceNumberService.CreateServiceNumberAsync(name, phone, description, serviceType, userId);
        //            return Json(new {
        //                success = true,
        //                message = "Service number created successfully",
        //                id = serviceNumber.Id
        //            });
        //        }
        //        else
        //        {
        //            // Update existing service number
        //            var success = await _serviceNumberService.UpdateServiceNumberAsync(id, name, phone, description, serviceType);
        //            return Json(new {
        //                success = success,
        //                message = success ? "Service number updated successfully" : "Service number not found"
        //            });
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error saving service number: {Name} - {Phone}", name, phone);
        //        return Json(new { success = false, message = "Error saving service number" });
        //    }
        //}

        /// <summary>
        /// API endpoint to delete service number
        /// </summary>
        /// <param name="id">Service number ID</param>
        ///// <returns>JSON response</returns>
        //[HttpDelete]
        //public async Task<IActionResult> DeleteServiceNumber(int id)
        //{
        //    try
        //    {
        //        _logger.LogInformation("Deleting service number ID: {Id}", id);

        //        var success = await _serviceNumberService.DeleteServiceNumberAsync(id);
        //        return Json(new {
        //            success = success,
        //            message = success ? "Service number deleted successfully" : "Service number not found"
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error deleting service number ID: {Id}", id);
        //        return Json(new { success = false, message = "Error deleting service number" });
        //    }
        //}

        ///// <summary>
        /// API endpoint to toggle service number status
        /// </summary>
        /// <param name="id">Service number ID</param>
        /// <returns>JSON response</returns>
        //[HttpPost]
        //public async Task<IActionResult> ToggleServiceNumber(int id)
        //{
        //    try
        //    {
        //        _logger.LogInformation("Toggling service number status for ID: {Id}", id);

        //        var success = await _serviceNumberService.ToggleServiceNumberStatusAsync(id);
        //        return Json(new {
        //            success = success,
        //            message = success ? "Service number status updated successfully" : "Service number not found"
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error toggling service number status for ID: {Id}", id);
        //        return Json(new { success = false, message = "Error updating service number status" });
        //    }
        //}

        /// <summary>
        /// API endpoint to get settings
        /// </summary>
        /// <returns>JSON response with settings</returns>
        [HttpGet]
        public async Task<IActionResult> GetSettings()
        {
            try
            {
                _logger.LogInformation("Getting WhatsApp settings");

                var settings = await _settingsService.GetAllSettingsAsync();
                var settingsData = settings.Select(s => new
                {
                    key = s.SettingKey,
                    value = s.SettingValue,
                    dataType = s.DataType,
                    description = s.Description,
                    isActive = s.IsActive
                });

                return Json(new { success = true, settings = settingsData });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting settings");
                return Json(new { success = false, message = "Error getting settings" });
            }
        }

        /// <summary>
        /// API endpoint to update settings
        /// </summary>
        /// <param name="settingName">Setting name</param>
        /// <param name="settingValue">Setting value</param>
        /// <returns>JSON response</returns>
        [HttpPost]
        public async Task<IActionResult> UpdateSetting(string settingName, string settingValue)
        {
            try
            {
                _logger.LogInformation("Updating setting: {SettingName} = {SettingValue}", settingName, settingValue);

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var success = await _settingsService.SetSettingAsync(settingName, settingValue, userId);

                return Json(new {
                    success = success,
                    message = success ? "Setting updated successfully" : "Error updating setting"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating setting: {SettingName}", settingName);
                return Json(new { success = false, message = "Error updating setting" });
            }
        }

        /// <summary>
        /// API endpoint to search chats
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>JSON response with filtered chats</returns>
        [HttpGet]
        public async Task<IActionResult> SearchChats(string searchTerm)
        {
            try
            {
                _logger.LogInformation("Searching chats with term: {SearchTerm}", searchTerm);

                var chats = await _whatsAppService.SearchChatsAsync(searchTerm);
                var chatData = chats.Select(c => new
                {
                    id = c.Id,
                    customerName = c.Customer.Name,
                    customerPhone = c.Customer.Number,
                 
                    lastMessage = c.LastMessage,
                    lastMessageAt = c.LastMessageAt.ToString("HH:mm"),
                    unreadCount = c.UnreadCount,
                   
                });

                return Json(new { success = true, chats = chatData });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching chats with term: {SearchTerm}", searchTerm);
                return Json(new { success = false, message = "Error searching chats" });
            }
        }

        /// <summary>
        /// API endpoint to get statistics
        /// </summary>
        /// <returns>JSON response with statistics</returns>
        [HttpGet]
        public async Task<IActionResult> GetStatistics()
        {
            try
            {
                _logger.LogInformation("Getting WhatsApp statistics");

                var totalChats = await _whatsAppService.GetTotalChatsCountAsync();
                var activeChats = await _whatsAppService.GetActiveChatsCountAsync();
                var pendingChats = await _whatsAppService.GetPendingChatsCountAsync();
                var inactiveChats = await _whatsAppService.GetInactiveChatsCountAsync();
                var unreadMessages = await _whatsAppService.GetUnreadMessagesCountAsync();

                var statistics = new
                {
                    totalChats = totalChats,
                    activeChats = activeChats,
                    pendingChats = pendingChats,
                    inactiveChats = inactiveChats,
                    unreadMessages = unreadMessages
                };

                return Json(new { success = true, statistics = statistics });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting statistics");
                return Json(new { success = false, message = "Error getting statistics" });
            }
        }

        /// <summary>
        /// API endpoint to mark messages as read
        /// </summary>
        /// <param name="chatId">Chat ID</param>
        /// <returns>JSON response</returns>
        [HttpPost]
        public async Task<IActionResult> MarkMessagesAsRead(int chatId)
        {
            try
            {
                _logger.LogInformation("Marking messages as read for chat ID: {ChatId}", chatId);

                var success = await _whatsAppService.MarkMessagesAsReadAsync(chatId);
                return Json(new {
                    success = success,
                    message = success ? "Messages marked as read" : "Error marking messages as read"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking messages as read for chat ID: {ChatId}", chatId);
                return Json(new { success = false, message = "Error marking messages as read" });
            }
        }
    }
}
