using Core;
using Core.Entities;
using Core.Service.Contract;
using Core.Specifications.AutoReplySpec;
using Microsoft.Extensions.Logging;

namespace Services
{
    public class AutoReplyService : IAutoReplyService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IWhatsAppSettingsService _settingsService;
        private readonly ILogger<AutoReplyService> _logger;

        public AutoReplyService(IUnitOfWork unitOfWork, IWhatsAppSettingsService settingsService, ILogger<AutoReplyService> logger)
        {
            _unitOfWork = unitOfWork;
            _settingsService = settingsService;
            _logger = logger;
        }

        public async Task<IEnumerable<AutoReply>> GetAllAutoRepliesAsync()
        {
            try
            {
                var spec = new GetAllAutoReplies();
                return await _unitOfWork.GetRepository<AutoReply>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all auto replies");
                throw;
            }
        }

        public async Task<IEnumerable<AutoReply>> GetActiveAutoRepliesAsync()
        {
            try
            {
                var spec = new GetActiveAutoReplies();
                return await _unitOfWork.GetRepository<AutoReply>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active auto replies");
                throw;
            }
        }

        public async Task<IEnumerable<AutoReply>> GetAutoRepliesByUserAsync(string userId)
        {
            try
            {
                var spec = new GetAllAutoReplies(userId);
                return await _unitOfWork.GetRepository<AutoReply>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto replies by user: {UserId}", userId);
                throw;
            }
        }

        public async Task<AutoReply?> GetAutoReplyByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.GetRepository<AutoReply>().GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto reply by ID: {Id}", id);
                throw;
            }
        }

        public async Task<AutoReply?> FindMatchingAutoReplyAsync(string messageContent)
        {
            try
            {
                var spec = new FindMatchingAutoReply(messageContent);
                var matchingReplies = await _unitOfWork.GetRepository<AutoReply>().GetAllSpecAsync(spec);
                return matchingReplies.FirstOrDefault(); // Returns highest priority match
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding matching auto reply for message: {MessageContent}", messageContent);
                return null;
            }
        }

        public async Task<AutoReply> CreateAutoReplyAsync(string trigger, string response, string? createdByUserId = null, 
            string matchType = "contains", bool isCaseSensitive = false, int priority = 1)
        {
            try
            {
                var autoReply = new AutoReply
                {
                    Trigger = trigger,
                    Response = response,
                    CreatedByUserId = createdByUserId,
                    MatchType = matchType,
                    IsCaseSensitive = isCaseSensitive,
                    Priority = priority,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                await _unitOfWork.GetRepository<AutoReply>().AddAsync(autoReply);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Created auto reply: Trigger={Trigger}, CreatedBy={CreatedBy}", trigger, createdByUserId);
                return autoReply;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating auto reply: Trigger={Trigger}", trigger);
                throw;
            }
        }

        public async Task<bool> UpdateAutoReplyAsync(int id, string trigger, string response, string matchType = "contains", 
            bool isCaseSensitive = false, int priority = 1)
        {
            try
            {
                var autoReply = await _unitOfWork.GetRepository<AutoReply>().GetByIdAsync(id);
                if (autoReply == null) return false;

                autoReply.Trigger = trigger;
                autoReply.Response = response;
                autoReply.MatchType = matchType;
                autoReply.IsCaseSensitive = isCaseSensitive;
                autoReply.Priority = priority;
                autoReply.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.GetRepository<AutoReply>().Update(autoReply);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Updated auto reply: Id={Id}, Trigger={Trigger}", id, trigger);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating auto reply: Id={Id}", id);
                return false;
            }
        }

        public async Task<bool> ToggleAutoReplyStatusAsync(int id)
        {
            try
            {
                var autoReply = await _unitOfWork.GetRepository<AutoReply>().GetByIdAsync(id);
                if (autoReply == null) return false;

                autoReply.IsActive = !autoReply.IsActive;
                autoReply.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.GetRepository<AutoReply>().Update(autoReply);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Toggled auto reply status: Id={Id}, IsActive={IsActive}", id, autoReply.IsActive);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling auto reply status: Id={Id}", id);
                return false;
            }
        }

        public async Task<bool> DeleteAutoReplyAsync(int id)
        {
            try
            {
                var autoReply = await _unitOfWork.GetRepository<AutoReply>().GetByIdAsync(id);
                if (autoReply == null) return false;

                _unitOfWork.GetRepository<AutoReply>().Delete(autoReply);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Deleted auto reply: Id={Id}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting auto reply: Id={Id}", id);
                return false;
            }
        }

        public async Task<bool> UpdateAutoReplyUsageAsync(int id)
        {
            try
            {
                var autoReply = await _unitOfWork.GetRepository<AutoReply>().GetByIdAsync(id);
                if (autoReply == null) return false;

                autoReply.UsageCount++;
                autoReply.LastUsedAt = DateTime.UtcNow;

                _unitOfWork.GetRepository<AutoReply>().Update(autoReply);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Updated auto reply usage: Id={Id}, UsageCount={UsageCount}", id, autoReply.UsageCount);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating auto reply usage: Id={Id}", id);
                return false;
            }
        }

        public async Task<string?> ProcessMessageForAutoReplyAsync(string messageContent)
        {
            try
            {
                // Check if auto reply is enabled
                if (!await IsAutoReplyEnabledAsync())
                {
                    return null;
                }

                var matchingReply = await FindMatchingAutoReplyAsync(messageContent);
                return matchingReply?.Response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing message for auto reply: {MessageContent}", messageContent);
                return null;
            }
        }

        public async Task<bool> IsAutoReplyEnabledAsync()
        {
            try
            {
                return await _settingsService.IsAutoReplyEnabledAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if auto reply is enabled");
                return false;
            }
        }
    }
}
