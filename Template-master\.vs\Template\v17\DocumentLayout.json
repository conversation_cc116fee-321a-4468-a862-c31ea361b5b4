{"Version": 1, "WorkspaceRootPath": "D:\\NET\\Projects\\templatewithView\\Template-master\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825175233_UpdateCus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Repository\\Data\\Migrations\\20250825175233_UpdateCus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\data\\configurations\\chatconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\data\\configurations\\chatconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\data\\configurations\\customerconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\data\\configurations\\customerconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\entities\\customer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\entities\\customer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\entities\\chat.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\entities\\chat.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825174719_UpdateCusto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Repository\\Data\\Migrations\\20250825174719_UpdateCusto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\data\\configurations\\whatsappsettingsconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\data\\configurations\\whatsappsettingsconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825174500_updatecusotmer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Repository\\Data\\Migrations\\20250825174500_updatecusotmer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\controllers\\whatsappwebhookcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\controllers\\whatsappwebhookcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\entities\\whatsappsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\entities\\whatsappsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\service.contract\\iwhatsappcloudeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\service.contract\\iwhatsappcloudeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|d:\\net\\projects\\templatewithview\\template-master\\services\\whatsapp\\whatsappcloudeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|solutionrelative:services\\whatsapp\\whatsappcloudeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\specifications\\customerspec\\getcustomerbyphone.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\specifications\\customerspec\\getcustomerbyphone.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|d:\\net\\projects\\templatewithview\\template-master\\services\\whatsapp\\whatsappsettingsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|solutionrelative:services\\whatsapp\\whatsappsettingsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|d:\\net\\projects\\templatewithview\\template-master\\services\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|solutionrelative:services\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\resluts\\whatsapptextrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\resluts\\whatsapptextrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\controllers\\whatsappcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\controllers\\whatsappcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\extentions\\applicationservicesextentions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\extentions\\applicationservicesextentions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\helper\\whatsappcloudesettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\helper\\whatsappcloudesettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\service.contract\\iwhatsappservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\service.contract\\iwhatsappservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|d:\\net\\projects\\templatewithview\\template-master\\services\\services.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|solutionrelative:services\\services.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|d:\\net\\projects\\templatewithview\\template-master\\services\\whatsapp\\whatsappservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|solutionrelative:services\\whatsapp\\whatsappservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\entities\\message.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\entities\\message.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\whatsapp\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\whatsapp\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\specifications\\chatspec\\searchchats.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\specifications\\chatspec\\searchchats.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825160757_customer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Repository\\Data\\Migrations\\20250825160757_customer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\data\\dataseeding\\identityseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\data\\dataseeding\\identityseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825160215_Iniail.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Repository\\Data\\Migrations\\20250825160215_Iniail.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825155624_fixing.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Repository\\Data\\Migrations\\20250825155624_fixing.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\repositories\\genericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\repositories\\genericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\controllers\\notificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\controllers\\notificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\specificationprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\specificationprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\specifications\\specification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\specifications\\specification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\specifications\\chatspec\\getallchats.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\specifications\\chatspec\\getallchats.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\repository.contract\\igenericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\repository.contract\\igenericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\whatsapp\\servicecustomers.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\whatsapp\\servicecustomers.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\whatsapp\\settings.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\whatsapp\\settings.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|d:\\net\\projects\\templatewithview\\template-master\\services\\autoreplyservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59763EC0-3EA9-4618-AA11-3721B2EFBCD4}|Services\\Services.csproj|solutionrelative:services\\autoreplyservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|d:\\net\\projects\\templatewithview\\template-master\\repository\\data\\configurations\\autoreplyconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AD1ECC58-A143-4CD3-9088-3B531992F7CC}|Repository\\Repository.csproj|solutionrelative:repository\\data\\configurations\\autoreplyconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\specifications\\messagespec\\getmessagesbychatid.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\specifications\\messagespec\\getmessagesbychatid.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\specifications\\autoreplyspec\\findmatchingautoreply.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\specifications\\autoreplyspec\\findmatchingautoreply.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\entities\\autoreply.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\entities\\autoreply.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\specifications\\chatspec\\getchatsbyassigneduser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\specifications\\chatspec\\getchatsbyassigneduser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\specifications\\chatspec\\getchatbyphone.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\specifications\\chatspec\\getchatbyphone.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\service.contract\\iemailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\service.contract\\iemailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\entities\\notification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\entities\\notification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\resetpasswordconfirmation.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\resetpasswordconfirmation.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\register.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\register.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\resetpasswordconfirmation.css||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\resetpasswordconfirmation.css||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\resetpassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\resetpassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\shared\\_authlayout.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\shared\\_authlayout.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\js\\auth\\resetpassword.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\js\\auth\\resetpassword.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\resetpassword.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\resetpassword.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\js\\auth\\rigester.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\js\\auth\\rigester.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\_viewimports.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\_viewimports.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\rigester.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\rigester.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\shared\\_layout.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\shared\\_layout.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\layout.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\layout.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\forgotpassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\forgotpassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\login.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\login.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\layout.css||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\layout.css||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\adminlte.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\adminlte.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\js\\adminlte.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\js\\adminlte.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\auth\\forgotpasswordconfirmation.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\auth\\forgotpasswordconfirmation.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\js\\auth\\login.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\js\\auth\\login.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\login.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\login.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\forgotpasswordconfirmation.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\forgotpasswordconfirmation.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\wwwroot\\css\\auth\\forgotpassword.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\wwwroot\\css\\auth\\forgotpassword.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\20250823141748_notification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Repository\\Data\\20250823141748_notification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|d:\\net\\projects\\templatewithview\\template-master\\core\\specifications\\notificationspec\\getbyuserid.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42C671F9-720E-4E05-B1DD-58AD87F0FE3A}|Core\\Core.csproj|solutionrelative:core\\specifications\\notificationspec\\getbyuserid.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\models\\notificationviewmodel\\notificationviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\models\\notificationviewmodel\\notificationviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\notification\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\notification\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\departmentusers.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\departmentusers.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\_departmentuserrow.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\_departmentuserrow.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\editprofile.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\editprofile.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\delete.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\delete.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\create.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\create.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\changepassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\changepassword.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\extentions\\dbpreprocessextention.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\extentions\\dbpreprocessextention.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\profile.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\profile.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\edit.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\edit.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|d:\\net\\projects\\templatewithview\\template-master\\pl\\views\\user\\details.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A7A86A-B858-43BD-B3A7-E7380859FDFA}|PL\\PL.csproj|solutionrelative:pl\\views\\user\\details.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{57d563b6-44a5-47df-85be-f4199ad6b651}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "20250825175233_UpdateCus.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825175233_UpdateCus.cs", "RelativeDocumentMoniker": "Repository\\Data\\Migrations\\20250825175233_UpdateCus.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825175233_UpdateCus.cs", "RelativeToolTip": "Repository\\Data\\Migrations\\20250825175233_UpdateCus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T17:52:33.959Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "20250825174719_UpdateCusto.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825174719_UpdateCusto.cs", "RelativeDocumentMoniker": "Repository\\Data\\Migrations\\20250825174719_UpdateCusto.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825174719_UpdateCusto.cs", "RelativeToolTip": "Repository\\Data\\Migrations\\20250825174719_UpdateCusto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T17:47:19.227Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "WhatsAppSettingsConfiguration.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Configurations\\WhatsAppSettingsConfiguration.cs", "RelativeDocumentMoniker": "Repository\\Data\\Configurations\\WhatsAppSettingsConfiguration.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Configurations\\WhatsAppSettingsConfiguration.cs", "RelativeToolTip": "Repository\\Data\\Configurations\\WhatsAppSettingsConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T17:46:15.222Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "20250825174500_updatecusotmer.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825174500_updatecusotmer.cs", "RelativeDocumentMoniker": "Repository\\Data\\Migrations\\20250825174500_updatecusotmer.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825174500_updatecusotmer.cs", "RelativeToolTip": "Repository\\Data\\Migrations\\20250825174500_updatecusotmer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T17:45:00.791Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ChatConfiguration.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Configurations\\ChatConfiguration.cs", "RelativeDocumentMoniker": "Repository\\Data\\Configurations\\ChatConfiguration.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Configurations\\ChatConfiguration.cs", "RelativeToolTip": "Repository\\Data\\Configurations\\ChatConfiguration.cs", "ViewState": "AgIAABAAAAAAAAAAAAAQwCYAAABWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T15:06:04.574Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "appsettings.json", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\appsettings.json", "RelativeDocumentMoniker": "PL\\appsettings.json", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\appsettings.json", "RelativeToolTip": "PL\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAADAQAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-25T15:59:04.64Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Chat.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\Chat.cs", "RelativeDocumentMoniker": "Core\\Entities\\Chat.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\Chat.cs", "RelativeToolTip": "Core\\Entities\\Chat.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAYAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T14:28:29.031Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Customer.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\Customer.cs", "RelativeDocumentMoniker": "Core\\Entities\\Customer.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\Customer.cs", "RelativeToolTip": "Core\\Entities\\Customer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T17:34:06.872Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CustomerConfiguration.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Configurations\\CustomerConfiguration.cs", "RelativeDocumentMoniker": "Repository\\Data\\Configurations\\CustomerConfiguration.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Configurations\\CustomerConfiguration.cs", "RelativeToolTip": "Repository\\Data\\Configurations\\CustomerConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T15:28:21.648Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "WhatsAppSettingsService.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\Whatsapp\\WhatsAppSettingsService.cs", "RelativeDocumentMoniker": "Services\\Whatsapp\\WhatsAppSettingsService.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\Whatsapp\\WhatsAppSettingsService.cs", "RelativeToolTip": "Services\\Whatsapp\\WhatsAppSettingsService.cs", "ViewState": "AgIAAFwAAAAAAAAAAAAswAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T16:54:39.344Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "IWhatsAppCloudeService.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Service.Contract\\IWhatsAppCloudeService.cs", "RelativeDocumentMoniker": "Core\\Service.Contract\\IWhatsAppCloudeService.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Service.Contract\\IWhatsAppCloudeService.cs", "RelativeToolTip": "Core\\Service.Contract\\IWhatsAppCloudeService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAwAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T16:29:07.019Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "GetCustomerByPhone.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\CustomerSpec\\GetCustomerByPhone.cs", "RelativeDocumentMoniker": "Core\\Specifications\\CustomerSpec\\GetCustomerByPhone.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\CustomerSpec\\GetCustomerByPhone.cs", "RelativeToolTip": "Core\\Specifications\\CustomerSpec\\GetCustomerByPhone.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwA0AAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T17:26:37.428Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "UserService.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\UserService.cs", "RelativeDocumentMoniker": "Services\\UserService.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\UserService.cs", "RelativeToolTip": "Services\\UserService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T17:28:31.807Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "WhatsAppSettings.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\WhatsAppSettings.cs", "RelativeDocumentMoniker": "Core\\Entities\\WhatsAppSettings.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\WhatsAppSettings.cs", "RelativeToolTip": "Core\\Entities\\WhatsAppSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T17:38:28.558Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "WhatsappCloudeService.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\Whatsapp\\WhatsappCloudeService.cs", "RelativeDocumentMoniker": "Services\\Whatsapp\\WhatsappCloudeService.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\Whatsapp\\WhatsappCloudeService.cs", "RelativeToolTip": "Services\\Whatsapp\\WhatsappCloudeService.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAUwBEAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T16:30:15.641Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "WhatsAppWebhookController.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\WhatsAppWebhookController.cs", "RelativeDocumentMoniker": "PL\\Controllers\\WhatsAppWebhookController.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\WhatsAppWebhookController.cs", "RelativeToolTip": "PL\\Controllers\\WhatsAppWebhookController.cs", "ViewState": "AgIAADYAAAAAAAAAAAAwwFIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T16:59:16.962Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "WhatsAppTextRequest.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Resluts\\WhatsAppTextRequest.cs", "RelativeDocumentMoniker": "Core\\Resluts\\WhatsAppTextRequest.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Resluts\\WhatsAppTextRequest.cs", "RelativeToolTip": "Core\\Resluts\\WhatsAppTextRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T16:58:29.674Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "Program.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Program.cs", "RelativeDocumentMoniker": "PL\\Program.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Program.cs", "RelativeToolTip": "PL\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAC0AAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:25:51.967Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "WhatsappController.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\WhatsappController.cs", "RelativeDocumentMoniker": "PL\\Controllers\\WhatsappController.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\WhatsappController.cs", "RelativeToolTip": "PL\\Controllers\\WhatsappController.cs", "ViewState": "AgIAAE8AAAAAAAAAAAAIwGEAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T12:53:51.962Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "AuthController.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "PL\\Controllers\\AuthController.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\AuthController.cs", "RelativeToolTip": "PL\\Controllers\\AuthController.cs", "ViewState": "AgIAABcAAAAAAAAAAADwvy8AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:33:56.568Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "ApplicationServicesExtentions.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Extentions\\ApplicationServicesExtentions.cs", "RelativeDocumentMoniker": "PL\\Extentions\\ApplicationServicesExtentions.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Extentions\\ApplicationServicesExtentions.cs", "RelativeToolTip": "PL\\Extentions\\ApplicationServicesExtentions.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAACYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:34:23.286Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "WhatsAppCloudeSettings.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Helper\\WhatsAppCloudeSettings.cs", "RelativeDocumentMoniker": "Core\\Helper\\WhatsAppCloudeSettings.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Helper\\WhatsAppCloudeSettings.cs", "RelativeToolTip": "Core\\Helper\\WhatsAppCloudeSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T16:31:40.688Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "Services", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\Services.csproj", "RelativeDocumentMoniker": "Services\\Services.csproj", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\Services.csproj", "RelativeToolTip": "Services\\Services.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-08-25T16:25:46.805Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "Core.csproj", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Core.csproj", "RelativeDocumentMoniker": "Core\\Core.csproj", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Core.csproj", "RelativeToolTip": "Core\\Core.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-08-25T15:01:28.942Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "IdentitySeeder.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\DataSeeding\\IdentitySeeder.cs", "RelativeDocumentMoniker": "Repository\\Data\\DataSeeding\\IdentitySeeder.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\DataSeeding\\IdentitySeeder.cs", "RelativeToolTip": "Repository\\Data\\DataSeeding\\IdentitySeeder.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABIAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:30:22.949Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "Index.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\Index.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Whatsapp\\Index.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\Index.cshtml", "RelativeToolTip": "PL\\Views\\Whatsapp\\Index.cshtml", "ViewState": "AgIAAI8BAAAAAAAAAAAjwKcBAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-25T12:47:46.517Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "IWhatsAppService.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Service.Contract\\IWhatsAppService.cs", "RelativeDocumentMoniker": "Core\\Service.Contract\\IWhatsAppService.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Service.Contract\\IWhatsAppService.cs", "RelativeToolTip": "Core\\Service.Contract\\IWhatsAppService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAkAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T14:49:41.283Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 32, "Title": "AppDbContext.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\AppDbContext.cs", "RelativeDocumentMoniker": "Repository\\Data\\AppDbContext.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\AppDbContext.cs", "RelativeToolTip": "Repository\\Data\\AppDbContext.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAwBAAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T14:20:15.123Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "WhatsAppService.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\Whatsapp\\WhatsAppService.cs", "RelativeDocumentMoniker": "Services\\Whatsapp\\WhatsAppService.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\Whatsapp\\WhatsAppService.cs", "RelativeToolTip": "Services\\Whatsapp\\WhatsAppService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAPYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T14:47:42.434Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 28, "Title": "SearchChats.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\ChatSpec\\SearchChats.cs", "RelativeDocumentMoniker": "Core\\Specifications\\ChatSpec\\SearchChats.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\ChatSpec\\SearchChats.cs", "RelativeToolTip": "Core\\Specifications\\ChatSpec\\SearchChats.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T14:51:37.453Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "Message.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\Message.cs", "RelativeDocumentMoniker": "Core\\Entities\\Message.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\Message.cs", "RelativeToolTip": "Core\\Entities\\Message.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAAwCYAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T14:33:09.759Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 29, "Title": "20250825160757_customer.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825160757_customer.cs", "RelativeDocumentMoniker": "Repository\\Data\\Migrations\\20250825160757_customer.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825160757_customer.cs", "RelativeToolTip": "Repository\\Data\\Migrations\\20250825160757_customer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T16:07:57.835Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 36, "Title": "SpecificationProvider.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\SpecificationProvider.cs", "RelativeDocumentMoniker": "Repository\\SpecificationProvider.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\SpecificationProvider.cs", "RelativeToolTip": "Repository\\SpecificationProvider.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAwwA4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:59:48.003Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 34, "Title": "GenericRepository.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Repositories\\GenericRepository.cs", "RelativeDocumentMoniker": "Repository\\Repositories\\GenericRepository.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Repositories\\GenericRepository.cs", "RelativeToolTip": "Repository\\Repositories\\GenericRepository.cs", "ViewState": "AgIAACAAAAAAAAAAAAAswC0AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:55:21.684Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "20250825160215_Iniail.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825160215_Iniail.cs", "RelativeDocumentMoniker": "Repository\\Data\\Migrations\\20250825160215_Iniail.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825160215_Iniail.cs", "RelativeToolTip": "Repository\\Data\\Migrations\\20250825160215_Iniail.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T16:02:15.137Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 33, "Title": "20250825155624_fixing.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825155624_fixing.cs", "RelativeDocumentMoniker": "Repository\\Data\\Migrations\\20250825155624_fixing.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Migrations\\20250825155624_fixing.cs", "RelativeToolTip": "Repository\\Data\\Migrations\\20250825155624_fixing.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T15:56:24.258Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 35, "Title": "NotificationController.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\NotificationController.cs", "RelativeDocumentMoniker": "PL\\Controllers\\NotificationController.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\NotificationController.cs", "RelativeToolTip": "PL\\Controllers\\NotificationController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAPoAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:34:54.176Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 37, "Title": "Specification.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\Specification.cs", "RelativeDocumentMoniker": "Core\\Specifications\\Specification.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\Specification.cs", "RelativeToolTip": "Core\\Specifications\\Specification.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T14:04:49.053Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 38, "Title": "GetAllChats.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\ChatSpec\\GetAllChats.cs", "RelativeDocumentMoniker": "Core\\Specifications\\ChatSpec\\GetAllChats.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\ChatSpec\\GetAllChats.cs", "RelativeToolTip": "Core\\Specifications\\ChatSpec\\GetAllChats.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T14:47:53.956Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 39, "Title": "IGenericRepository.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Repository.Contract\\IGenericRepository.cs", "RelativeDocumentMoniker": "Core\\Repository.Contract\\IGenericRepository.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Repository.Contract\\IGenericRepository.cs", "RelativeToolTip": "Core\\Repository.Contract\\IGenericRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:55:45.825Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 40, "Title": "ServiceCustomers.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\ServiceCustomers.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Whatsapp\\ServiceCustomers.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\ServiceCustomers.cshtml", "RelativeToolTip": "PL\\Views\\Whatsapp\\ServiceCustomers.cshtml", "ViewState": "AgIAAAcBAAAAAAAAAIBJwBQBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-25T12:48:00.74Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 41, "Title": "Settings.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\Settings.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Whatsapp\\Settings.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Whatsapp\\Settings.cshtml", "RelativeToolTip": "PL\\Views\\Whatsapp\\Settings.cshtml", "ViewState": "AgIAAJIBAAAAAAAAAAAkwJ0BAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-25T12:48:46.48Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 47, "Title": "GetChatsByAssignedUser.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\ChatSpec\\GetChatsByAssignedUser.cs", "RelativeDocumentMoniker": "Core\\Specifications\\ChatSpec\\GetChatsByAssignedUser.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\ChatSpec\\GetChatsByAssignedUser.cs", "RelativeToolTip": "Core\\Specifications\\ChatSpec\\GetChatsByAssignedUser.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T14:50:27.293Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 48, "Title": "GetChatByPhone.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\ChatSpec\\GetChatByPhone.cs", "RelativeDocumentMoniker": "Core\\Specifications\\ChatSpec\\GetChatByPhone.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\ChatSpec\\GetChatByPhone.cs", "RelativeToolTip": "Core\\Specifications\\ChatSpec\\GetChatByPhone.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAB1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T14:50:58.676Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 44, "Title": "GetMessagesByChatId.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\MessageSpec\\GetMessagesByChatId.cs", "RelativeDocumentMoniker": "Core\\Specifications\\MessageSpec\\GetMessagesByChatId.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\MessageSpec\\GetMessagesByChatId.cs", "RelativeToolTip": "Core\\Specifications\\MessageSpec\\GetMessagesByChatId.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB8AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T15:03:29.644Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 43, "Title": "AutoReplyConfiguration.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Configurations\\AutoReplyConfiguration.cs", "RelativeDocumentMoniker": "Repository\\Data\\Configurations\\AutoReplyConfiguration.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\Configurations\\AutoReplyConfiguration.cs", "RelativeToolTip": "Repository\\Data\\Configurations\\AutoReplyConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACEAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T15:05:43.482Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 42, "Title": "AutoReplyService.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\AutoReplyService.cs", "RelativeDocumentMoniker": "Services\\AutoReplyService.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Services\\AutoReplyService.cs", "RelativeToolTip": "Services\\AutoReplyService.cs", "ViewState": "AgIAALYAAAAAAAAAAAAwwMgAAABnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T15:07:22.865Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 45, "Title": "FindMatchingAutoReply.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\AutoReplySpec\\FindMatchingAutoReply.cs", "RelativeDocumentMoniker": "Core\\Specifications\\AutoReplySpec\\FindMatchingAutoReply.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\AutoReplySpec\\FindMatchingAutoReply.cs", "RelativeToolTip": "Core\\Specifications\\AutoReplySpec\\FindMatchingAutoReply.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T15:02:00.792Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 46, "Title": "AutoReply.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\AutoReply.cs", "RelativeDocumentMoniker": "Core\\Entities\\AutoReply.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\AutoReply.cs", "RelativeToolTip": "Core\\Entities\\AutoReply.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBJwAgAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T14:25:13.706Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 49, "Title": "IEmailService.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Service.Contract\\IEmailService.cs", "RelativeDocumentMoniker": "Core\\Service.Contract\\IEmailService.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Service.Contract\\IEmailService.cs", "RelativeToolTip": "Core\\Service.Contract\\IEmailService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T14:39:40.808Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 50, "Title": "Notification.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\Notification.cs", "RelativeDocumentMoniker": "Core\\Entities\\Notification.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Entities\\Notification.cs", "RelativeToolTip": "Core\\Entities\\Notification.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAgAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:39:02.303Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 53, "Title": "ResetPasswordConfirmation.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T18:37:00.623Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 51, "Title": "ResetPasswordConfirmation.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ResetPasswordConfirmation.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\ResetPasswordConfirmation.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ResetPasswordConfirmation.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\ResetPasswordConfirmation.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T18:36:14.316Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 56, "Title": "ResetPassword.js", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\ResetPassword.js", "RelativeDocumentMoniker": "PL\\wwwroot\\js\\Auth\\ResetPassword.js", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\ResetPassword.js", "RelativeToolTip": "PL\\wwwroot\\js\\Auth\\ResetPassword.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-23T18:35:16.351Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 57, "Title": "ResetPassword.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPassword.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\ResetPassword.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPassword.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\ResetPassword.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T18:33:24.941Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 55, "Title": "_AuthLayout.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Shared\\_AuthLayout.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Shared\\_AuthLayout.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Shared\\_AuthLayout.cshtml", "RelativeToolTip": "PL\\Views\\Shared\\_AuthLayout.cshtml", "ViewState": "AgIAAAsAAAAAAAAAAABRwA8AAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T17:18:53.732Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 60, "Title": "Rigester.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Rigester.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\Rigester.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Rigester.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\Rigester.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T17:06:12.493Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 52, "Title": "Register.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\Register.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\Register.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\Register.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\Register.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:27:21.431Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 58, "Title": "Rigester.js", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Rigester.js", "RelativeDocumentMoniker": "PL\\wwwroot\\js\\Auth\\Rigester.js", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Rigester.js", "RelativeToolTip": "PL\\wwwroot\\js\\Auth\\Rigester.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-23T17:07:31.63Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 67, "Title": "adminlte.js", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.js", "RelativeDocumentMoniker": "PL\\wwwroot\\js\\adminlte.js", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.js", "RelativeToolTip": "PL\\wwwroot\\js\\adminlte.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-23T17:20:20.096Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 54, "Title": "ResetPassword.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ResetPassword.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\ResetPassword.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ResetPassword.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\ResetPassword.cshtml", "ViewState": "AgIAAEEAAAAAAAAAAIBJwFAAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T17:05:03.819Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 61, "Title": "_Layout.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Shared\\_Layout.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Shared\\_Layout.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Shared\\_Layout.cshtml", "RelativeToolTip": "PL\\Views\\Shared\\_Layout.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:26:24.695Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 59, "Title": "_ViewImports.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\_ViewImports.cshtml", "RelativeDocumentMoniker": "PL\\Views\\_ViewImports.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\_ViewImports.cshtml", "RelativeToolTip": "PL\\Views\\_ViewImports.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:13:02.075Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 66, "Title": "adminlte.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\adminlte.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.css", "RelativeToolTip": "PL\\wwwroot\\css\\adminlte.css", "ViewState": "AgIAAADPAAAAAAAAAIBJwBPPAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T17:20:25.253Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 62, "Title": "Layout.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Layout.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\Layout.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Layout.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\Layout.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T18:26:04.567Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 64, "Title": "Login.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\Login.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\Login.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\Login.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\Login.cshtml", "ViewState": "AgIAACcAAAAAAAAAAIBJwDsAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T14:47:34.887Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 63, "Title": "ForgotPassword.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ForgotPassword.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\ForgotPassword.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ForgotPassword.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\ForgotPassword.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T14:26:22.928Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 65, "Title": "Layout.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Layout.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\Layout.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Layout.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\Layout.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T17:25:22.193Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 68, "Title": "ForgotPasswordConfirmation.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ForgotPasswordConfirmation.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Auth\\ForgotPasswordConfirmation.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Auth\\ForgotPasswordConfirmation.cshtml", "RelativeToolTip": "PL\\Views\\Auth\\ForgotPasswordConfirmation.cshtml", "ViewState": "AgIAAAEAAAAAAAAAAIBJwAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T14:45:42.735Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 71, "Title": "ForgotPasswordConfirmation.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T14:46:25.993Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 70, "Title": "Login.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Login.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\Login.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Login.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\Login.css", "ViewState": "AgIAAFAAAAAAAAAAAEBVwGMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T14:48:02.761Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 72, "Title": "ForgotPassword.css", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPassword.css", "RelativeDocumentMoniker": "PL\\wwwroot\\css\\Auth\\ForgotPassword.css", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPassword.css", "RelativeToolTip": "PL\\wwwroot\\css\\Auth\\ForgotPassword.css", "ViewState": "AgIAADkAAAAAAAAAAIBJwEYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-23T14:26:12.383Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 69, "Title": "Login.js", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Login.js", "RelativeDocumentMoniker": "PL\\wwwroot\\js\\Auth\\Login.js", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Login.js", "RelativeToolTip": "PL\\wwwroot\\js\\Auth\\Login.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-23T14:49:04.114Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 74, "Title": "GetByUserId.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\NotificationSpec\\GetByUserId.cs", "RelativeDocumentMoniker": "Core\\Specifications\\NotificationSpec\\GetByUserId.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Core\\Specifications\\NotificationSpec\\GetByUserId.cs", "RelativeToolTip": "Core\\Specifications\\NotificationSpec\\GetByUserId.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAABkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T14:04:51.954Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 73, "Title": "20250823141748_notification.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\20250823141748_notification.cs", "RelativeDocumentMoniker": "Repository\\Data\\20250823141748_notification.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\Repository\\Data\\20250823141748_notification.cs", "RelativeToolTip": "Repository\\Data\\20250823141748_notification.cs", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T14:17:48.813Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "Delete.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Delete.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Delete.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Delete.cshtml", "RelativeToolTip": "PL\\Views\\User\\Delete.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T13:11:37.355Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 76, "Title": "NotificationViewModel.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Models\\NotificationViewModel\\NotificationViewModel.cs", "RelativeDocumentMoniker": "PL\\Models\\NotificationViewModel\\NotificationViewModel.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Models\\NotificationViewModel\\NotificationViewModel.cs", "RelativeToolTip": "PL\\Models\\NotificationViewModel\\NotificationViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T13:32:55.862Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 75, "Title": "UserController.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\UserController.cs", "RelativeDocumentMoniker": "PL\\Controllers\\UserController.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Controllers\\UserController.cs", "RelativeToolTip": "PL\\Controllers\\UserController.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAwwCIAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:16:08.564Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 78, "Title": "DepartmentUsers.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\DepartmentUsers.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\DepartmentUsers.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\DepartmentUsers.cshtml", "RelativeToolTip": "PL\\Views\\User\\DepartmentUsers.cshtml", "ViewState": "AgIAALEAAAAAAAAAAEBVwFIAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T13:17:30.243Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 77, "Title": "Index.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Notification\\Index.cshtml", "RelativeDocumentMoniker": "PL\\Views\\Notification\\Index.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\Notification\\Index.cshtml", "RelativeToolTip": "PL\\Views\\Notification\\Index.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T13:31:49.543Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 80, "Title": "EditProfile.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\EditProfile.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\EditProfile.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\EditProfile.cshtml", "RelativeToolTip": "PL\\Views\\User\\EditProfile.cshtml", "ViewState": "AgIAADAAAAAAAAAAAIBZwD4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T13:13:09.219Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 84, "Title": "DbPreProcessExtention.cs", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Extentions\\DbPreProcessExtention.cs", "RelativeDocumentMoniker": "PL\\Extentions\\DbPreProcessExtention.cs", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Extentions\\DbPreProcessExtention.cs", "RelativeToolTip": "PL\\Extentions\\DbPreProcessExtention.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAowBsAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T12:31:40.687Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 85, "Title": "Profile.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Profile.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Profile.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Profile.cshtml", "RelativeToolTip": "PL\\Views\\User\\Profile.cshtml", "ViewState": "AgIAAEQAAAAAAAAAAEBVwFgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:14:53.79Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 86, "Title": "Index.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Index.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Index.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Index.cshtml", "RelativeToolTip": "PL\\Views\\User\\Index.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:14:24.114Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 87, "Title": "Edit.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Edit.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Edit.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Edit.cshtml", "RelativeToolTip": "PL\\Views\\User\\Edit.cshtml", "ViewState": "AgIAABIAAAAAAAAAAIBJwDwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:13:58.874Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 88, "Title": "Details.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Details.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Details.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Details.cshtml", "RelativeToolTip": "PL\\Views\\User\\Details.cshtml", "ViewState": "AgIAAEgAAAAAAAAAAIBJwFoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:13:39.256Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 82, "Title": "Create.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Create.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\Create.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\Create.cshtml", "RelativeToolTip": "PL\\Views\\User\\Create.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:13:16.623Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 83, "Title": "ChangePassword.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\ChangePassword.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\ChangePassword.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\ChangePassword.cshtml", "RelativeToolTip": "PL\\Views\\User\\ChangePassword.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:12:50.867Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 79, "Title": "_DepartmentUserRow.cshtml", "DocumentMoniker": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\_DepartmentUserRow.cshtml", "RelativeDocumentMoniker": "PL\\Views\\User\\_DepartmentUserRow.cshtml", "ToolTip": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\Views\\User\\_DepartmentUserRow.cshtml", "RelativeToolTip": "PL\\Views\\User\\_DepartmentUserRow.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-23T12:12:48.717Z", "EditorCaption": ""}]}, {"DockedWidth": 200, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}]}]}]}