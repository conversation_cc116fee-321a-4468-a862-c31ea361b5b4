﻿@{
    ViewData["Title"] = "تم إعادة تعيين كلمة المرور بنجاح";
    Layout = "~/Views/Shared/_AuthLayout.cshtml";
}

<link rel="stylesheet" href="~/css/Auth/ResetPasswordConfirmation.css">


<div class="success-container">
    <div class="card shadow-lg border-0 rounded-4">
        <div class="card-header bg-success text-white text-center py-3 rounded-top-4">
            <h3 class="mb-0 fs-2">تم إعادة تعيين كلمة المرور بنجاح</h3>
        </div>
        <div class="card-body p-5 text-center">
            <div class="success-animation mb-4">
                <div class="checkmark-circle">
                    <div class="checkmark draw"></div>
                </div>
            </div>

            <div class="alert alert-success shadow-sm p-4 fs-5">
                <p class="mb-3">تم إعادة تعيين كلمة المرور الخاصة بك بنجاح.</p>
                <p class="mb-0">يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.</p>
            </div>

            <div class="mt-5">
                <a asp-action="Login" class="btn btn-primary btn-lg fs-5 py-3 px-5 shadow-sm">
                    <i class="fas fa-sign-in-alt me-2"></i> العودة لتسجيل الدخول
                </a>
            </div>
        </div>
    </div>
</div>

