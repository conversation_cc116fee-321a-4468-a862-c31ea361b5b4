using Core.Entities;

namespace Core.Specifications.AutoReplySpec
{
    public class GetAllAutoReplies : Specification<AutoReply>
    {
        public GetAllAutoReplies() : base()
        {
            includes.Add(ar => ar.CreatedByUser);
            OrderByDesc = ar => ar.Priority;
        }

        public GetAllAutoReplies(bool isActive) : base(ar => ar.IsActive == isActive)
        {
            includes.Add(ar => ar.CreatedByUser);
            OrderByDesc = ar => ar.Priority;
        }

        public GetAllAutoReplies(string createdByUserId) : base(ar => ar.CreatedByUserId == createdByUserId)
        {
            includes.Add(ar => ar.CreatedByUser);
            OrderByDesc = ar => ar.Priority;
        }
    }
}
