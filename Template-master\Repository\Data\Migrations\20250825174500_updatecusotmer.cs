﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Repository.Data.Migrations
{
    /// <inheritdoc />
    public partial class updatecusotmer : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ChatId",
                table: "Customers",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "ChatId1",
                table: "Customers",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_Customers_ChatId1",
                table: "Customers",
                column: "ChatId1");

            migrationBuilder.AddForeignKey(
                name: "FK_Customers_Chats_ChatId1",
                table: "Customers",
                column: "ChatId1",
                principalTable: "Chats",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Customers_Chats_ChatId1",
                table: "Customers");

            migrationBuilder.DropIndex(
                name: "IX_Customers_ChatId1",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "ChatId",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "ChatId1",
                table: "Customers");
        }
    }
}
