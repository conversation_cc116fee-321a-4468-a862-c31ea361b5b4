using Core;
using Core.Entities;
using Core.Service.Contract;
using Core.Specifications.ServiceNumberSpec;
using Microsoft.Extensions.Logging;

namespace Services
{
    public class ServiceNumberService : IServiceNumberService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ServiceNumberService> _logger;

        public ServiceNumberService(IUnitOfWork unitOfWork, ILogger<ServiceNumberService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<ServiceNumber>> GetAllServiceNumbersAsync()
        {
            try
            {
                var spec = new GetAllServiceNumbers();
                return await _unitOfWork.GetRepository<ServiceNumber>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all service numbers");
                throw;
            }
        }

        public async Task<IEnumerable<ServiceNumber>> GetActiveServiceNumbersAsync()
        {
            try
            {
                var spec = new GetAllServiceNumbers(true);
                return await _unitOfWork.GetRepository<ServiceNumber>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active service numbers");
                throw;
            }
        }

        public async Task<IEnumerable<ServiceNumber>> GetServiceNumbersByTypeAsync(string serviceType)
        {
            try
            {
                var spec = new GetAllServiceNumbers(serviceType, true);
                return await _unitOfWork.GetRepository<ServiceNumber>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting service numbers by type: {ServiceType}", serviceType);
                throw;
            }
        }

        public async Task<IEnumerable<ServiceNumber>> GetServiceNumbersByUserAsync(string userId)
        {
            try
            {
                var spec = new GetServiceNumbersByUser(userId, true);
                return await _unitOfWork.GetRepository<ServiceNumber>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting service numbers by user: {UserId}", userId);
                throw;
            }
        }

        public async Task<ServiceNumber?> GetServiceNumberByIdAsync(int id)
        {
            try
            {
                return await _unitOfWork.GetRepository<ServiceNumber>().GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting service number by ID: {Id}", id);
                throw;
            }
        }

        public async Task<ServiceNumber> CreateServiceNumberAsync(string name, string phoneNumber, string? description = null, 
            string serviceType = "support", string? createdByUserId = null)
        {
            try
            {
                var serviceNumber = new ServiceNumber
                {
                    Name = name,
                    PhoneNumber = phoneNumber,
                    Description = description,
                    ServiceType = serviceType,
                    CreatedByUserId = createdByUserId,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    DisplayOrder = await GetNextDisplayOrderAsync()
                };

                await _unitOfWork.GetRepository<ServiceNumber>().AddAsync(serviceNumber);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Created service number: Name={Name}, Phone={Phone}", name, phoneNumber);
                return serviceNumber;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating service number: Name={Name}, Phone={Phone}", name, phoneNumber);
                throw;
            }
        }

        public async Task<bool> UpdateServiceNumberAsync(int id, string name, string phoneNumber, string? description = null, 
            string serviceType = "support")
        {
            try
            {
                var serviceNumber = await _unitOfWork.GetRepository<ServiceNumber>().GetByIdAsync(id);
                if (serviceNumber == null) return false;

                serviceNumber.Name = name;
                serviceNumber.PhoneNumber = phoneNumber;
                serviceNumber.Description = description;
                serviceNumber.ServiceType = serviceType;
                serviceNumber.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.GetRepository<ServiceNumber>().Update(serviceNumber);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Updated service number: Id={Id}, Name={Name}", id, name);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating service number: Id={Id}", id);
                return false;
            }
        }

        public async Task<bool> ToggleServiceNumberStatusAsync(int id)
        {
            try
            {
                var serviceNumber = await _unitOfWork.GetRepository<ServiceNumber>().GetByIdAsync(id);
                if (serviceNumber == null) return false;

                serviceNumber.IsActive = !serviceNumber.IsActive;
                serviceNumber.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.GetRepository<ServiceNumber>().Update(serviceNumber);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Toggled service number status: Id={Id}, IsActive={IsActive}", id, serviceNumber.IsActive);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling service number status: Id={Id}", id);
                return false;
            }
        }

        public async Task<bool> UpdateServiceNumberOrderAsync(int id, int displayOrder)
        {
            try
            {
                var serviceNumber = await _unitOfWork.GetRepository<ServiceNumber>().GetByIdAsync(id);
                if (serviceNumber == null) return false;

                serviceNumber.DisplayOrder = displayOrder;
                serviceNumber.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.GetRepository<ServiceNumber>().Update(serviceNumber);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Updated service number order: Id={Id}, DisplayOrder={DisplayOrder}", id, displayOrder);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating service number order: Id={Id}", id);
                return false;
            }
        }

        public async Task<bool> AssignUserToServiceNumberAsync(int serviceNumberId, string userId)
        {
            try
            {
                var serviceNumber = await _unitOfWork.GetRepository<ServiceNumber>().GetByIdAsync(serviceNumberId);
                if (serviceNumber == null) return false;

                // For now, we'll just track the assignment in the service number entity
                // In a real implementation, you might want to use UserManager or a separate junction table
                _unitOfWork.GetRepository<ServiceNumber>().Update(serviceNumber);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Assigned user to service number: ServiceNumberId={ServiceNumberId}, UserId={UserId}",
                    serviceNumberId, userId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning user to service number: ServiceNumberId={ServiceNumberId}, UserId={UserId}",
                    serviceNumberId, userId);
                return false;
            }
        }

        public async Task<bool> UnassignUserFromServiceNumberAsync(int serviceNumberId, string userId)
        {
            try
            {
                var serviceNumber = await _unitOfWork.GetRepository<ServiceNumber>().GetByIdAsync(serviceNumberId);
                if (serviceNumber == null) return false;

                // For now, we'll just update the service number
                // In a real implementation, you might want to use UserManager or a separate junction table
                _unitOfWork.GetRepository<ServiceNumber>().Update(serviceNumber);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Unassigned user from service number: ServiceNumberId={ServiceNumberId}, UserId={UserId}",
                    serviceNumberId, userId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unassigning user from service number: ServiceNumberId={ServiceNumberId}, UserId={UserId}",
                    serviceNumberId, userId);
                return false;
            }
        }

        public async Task<bool> DeleteServiceNumberAsync(int id)
        {
            try
            {
                var serviceNumber = await _unitOfWork.GetRepository<ServiceNumber>().GetByIdAsync(id);
                if (serviceNumber == null) return false;

                _unitOfWork.GetRepository<ServiceNumber>().Delete(serviceNumber);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Deleted service number: Id={Id}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting service number: Id={Id}", id);
                return false;
            }
        }

        public async Task<bool> UpdateContactCountAsync(int id)
        {
            try
            {
                var serviceNumber = await _unitOfWork.GetRepository<ServiceNumber>().GetByIdAsync(id);
                if (serviceNumber == null) return false;

                serviceNumber.ContactCount++;
                serviceNumber.LastContactAt = DateTime.UtcNow;

                _unitOfWork.GetRepository<ServiceNumber>().Update(serviceNumber);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Updated contact count: Id={Id}, ContactCount={ContactCount}", id, serviceNumber.ContactCount);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating contact count: Id={Id}", id);
                return false;
            }
        }

        public async Task<int> GetTotalServiceNumbersCountAsync()
        {
            try
            {
                var spec = new GetAllServiceNumbers();
                return await _unitOfWork.GetRepository<ServiceNumber>().CountSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total service numbers count");
                return 0;
            }
        }

        public async Task<int> GetActiveServiceNumbersCountAsync()
        {
            try
            {
                var spec = new GetAllServiceNumbers(true);
                return await _unitOfWork.GetRepository<ServiceNumber>().CountSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active service numbers count");
                return 0;
            }
        }

        public async Task<Dictionary<string, int>> GetServiceNumbersCountByTypeAsync()
        {
            try
            {
                var allServiceNumbers = await GetActiveServiceNumbersAsync();
                return allServiceNumbers
                    .GroupBy(sn => sn.ServiceType)
                    .ToDictionary(g => g.Key, g => g.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting service numbers count by type");
                return new Dictionary<string, int>();
            }
        }

        private async Task<int> GetNextDisplayOrderAsync()
        {
            try
            {
                var allServiceNumbers = await GetAllServiceNumbersAsync();
                return allServiceNumbers.Any() ? allServiceNumbers.Max(sn => sn.DisplayOrder) + 1 : 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting next display order");
                return 1;
            }
        }
    }
}
