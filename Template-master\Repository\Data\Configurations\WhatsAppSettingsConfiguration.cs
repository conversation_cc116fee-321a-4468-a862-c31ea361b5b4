using Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Repository.Data.Configurations
{
    public class WhatsAppSettingsConfiguration : IEntityTypeConfiguration<WhatsAppSettings>
    {
        public void Configure(EntityTypeBuilder<WhatsAppSettings> builder)
        {
            builder.HasKey(ws => ws.Id);

            builder.Property(ws => ws.SettingKey)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(ws => ws.SettingValue)
                .IsRequired()
                .HasMaxLength(2000);

            builder.Property(ws => ws.Description)
                .HasMaxLength(500);

            builder.Property(ws => ws.DataType)
                .IsRequired()
                .HasMaxLength(20)
                .HasDefaultValue("string");

            builder.Property(ws => ws.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(ws => ws.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");

            // Relationships
            builder.HasOne(ws => ws.UpdatedByUser)
                .WithMany()
                .HasForeignKey(ws => ws.UpdatedByUserId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(ws => ws.SettingKey)
                .IsUnique();

            builder.HasIndex(ws => ws.IsActive);

            builder.HasIndex(ws => ws.DataType);

            builder.HasIndex(ws => ws.UpdatedByUserId);

            builder.HasIndex(ws => new { ws.SettingKey, ws.IsActive });
        }
    }
}
