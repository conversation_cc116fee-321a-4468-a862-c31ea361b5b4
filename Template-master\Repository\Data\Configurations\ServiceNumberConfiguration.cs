using Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Repository.Data.Configurations
{
    public class ServiceNumberConfiguration : IEntityTypeConfiguration<ServiceNumber>
    {
        public void Configure(EntityTypeBuilder<ServiceNumber> builder)
        {
            builder.HasKey(sn => sn.Id);

            builder.Property(sn => sn.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(sn => sn.PhoneNumber)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(sn => sn.Description)
                .HasMaxLength(500);

            builder.Property(sn => sn.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(sn => sn.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");

            builder.Property(sn => sn.ServiceType)
                .IsRequired()
                .HasMaxLength(20)
                .HasDefaultValue("support");

            builder.Property(sn => sn.Department)
                .HasMaxLength(100);

            builder.Property(sn => sn.WorkingHours)
                .HasMaxLength(100);

            builder.Property(sn => sn.DisplayOrder)
                .HasDefaultValue(1);

            builder.Property(sn => sn.ContactCount)
                .HasDefaultValue(0);

            // Relationships
            builder.HasOne(sn => sn.CreatedByUser)
                .WithMany()
                .HasForeignKey(sn => sn.CreatedByUserId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasMany(sn => sn.AssignedUsers)
                .WithMany()
                .UsingEntity<Dictionary<string, object>>(
                    "ServiceNumberUser",
                    j => j.HasOne<User>().WithMany().HasForeignKey("UserId"),
                    j => j.HasOne<ServiceNumber>().WithMany().HasForeignKey("ServiceNumberId"),
                    j =>
                    {
                        j.HasKey("ServiceNumberId", "UserId");
                        j.ToTable("ServiceNumberUsers");
                    });

            // Indexes
            builder.HasIndex(sn => sn.Name);

            builder.HasIndex(sn => sn.PhoneNumber);

            builder.HasIndex(sn => sn.IsActive);

            builder.HasIndex(sn => sn.ServiceType);

            builder.HasIndex(sn => sn.DisplayOrder);

            builder.HasIndex(sn => sn.CreatedByUserId);

            builder.HasIndex(sn => new { sn.IsActive, sn.DisplayOrder });

            builder.HasIndex(sn => new { sn.ServiceType, sn.IsActive });
        }
    }
}
