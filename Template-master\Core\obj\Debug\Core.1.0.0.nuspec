﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Core</id>
    <version>1.0.0</version>
    <authors>Core</authors>
    <description>Package Description</description>
    <repository type="git" commit="2c1db331ec9bada840c885e31af96a1c85d48002" />
    <dependencies>
      <group targetFramework="net9.0">
        <dependency id="Microsoft.EntityFrameworkCore" version="9.0.4" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.SqlServer" version="9.0.4" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Identity.Core" version="9.0.4" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Identity.Stores" version="9.0.4" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\NET\Projects\templatewithView\Template-master\Core\bin\Debug\net9.0\Core.runtimeconfig.json" target="lib\net9.0\Core.runtimeconfig.json" />
    <file src="D:\NET\Projects\templatewithView\Template-master\Core\bin\Debug\net9.0\Core.dll" target="lib\net9.0\Core.dll" />
  </files>
</package>