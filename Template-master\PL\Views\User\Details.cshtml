@model User
@{
    ViewData["Title"] = "تفاصيل المستخدم";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- Bootstrap Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">

<!-- Custom Styles -->
<style>
    .card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }

    .detail-item {
        font-size: 16px;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f0f0f0;
    }

    .detail-icon {
        font-size: 20px;
        color: #0d6efd;
        margin-right: 8px;
        width: 28px;
        text-align: center;
    }

    .btn:hover {
        transform: scale(1.05);
        transition: 0.3s ease-in-out;
    }

    .badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
        margin-right: 5px;
    }

    .roles-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
    }

    .asset-table {
        margin-top: 15px;
    }

    .table th {
        background-color: #f5f5f5;
    }
</style>

<!-- Page Content -->
<div class="container mt-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h3 class="mb-0">
                <i class="bi bi-person-fill me-2"></i> تفاصيل المستخدم
            </h3>
            <div>
                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-light btn-sm">
                    <i class="bi bi-pencil-square"></i> تعديل
                </a>
                <a asp-action="ManageRoles" asp-route-userId="@Model.Id" class="btn btn-light btn-sm">
                    <i class="bi bi-person-gear"></i> إدارة الأدوار
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- المعلومات الأساسية -->
                <div class="col-md-6">
                    <h4 class="text-primary mb-3">المعلومات الأساسية</h4>

                    <div class="detail-item">
                        <i class="bi bi-person-circle detail-icon"></i>
                        <strong>الاسم الكامل:</strong> @Model.FullName
                    </div>

                    <div class="detail-item">
                        <i class="bi bi-envelope detail-icon"></i>
                        <strong>البريد الإلكتروني:</strong> @Model.Email
                    </div>
                </div>

                <!-- معلومات المؤسسة -->
                <div class="col-md-6">
                    <h4 class="text-primary mb-3">معلومات المؤسسة</h4>

                    <div class="detail-item">
                        <i class="bi bi-building detail-icon"></i>

                    </div>

                    <div class="detail-item">
                        <i class="bi bi-shield-check detail-icon"></i>
                        <strong>الحالة:</strong>
                        @if (Model.LockoutEnabled && Model.LockoutEnd > DateTimeOffset.Now)
                        {
                            <span class="badge bg-danger">مقفل</span>
                        }
                        else
                        {
                            <span class="badge bg-success">نشط</span>
                        }
                    </div>

                    <!-- أدوار المستخدم -->
                    <div class="roles-section">
                        <h5><i class="bi bi-person-badge me-2"></i> أدوار المستخدم</h5>
                        <div id="userRoles">
                            @if (ViewBag.UserRoles != null && ViewBag.UserRoles.Count > 0)
                            {
                                @foreach (var role in ViewBag.UserRoles)
                                {
                                    <span class="badge bg-primary">@role</span>
                                }
                            }
                            else
                            {
                                <span class="text-muted">لا توجد أدوار معينة</span>
                            }
                        </div>
                    </div>
                </div>
            </div>

         
            <!-- Assigned Assets Section -->
       @*      @if (Model.Assets != null && Model.Assets.Any())
            {
                <hr class="my-4">
                <h4 class="text-primary mb-3">
                    <i class="bi bi-box-seam me-2"></i>الأصول (@Model.Assets.Count())
                </h4>
                <div class="table-responsive asset-table">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Asset Tag</th>
                                <th>Description</th>
                                <th>Brand / Model</th>
                                <th>Status</th>
                                 
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var asset in Model.Assets)
                            {
                                <tr>
                                    <td>@asset.AssetTag</td>
                                    <td>@(asset.AssetDescription ?? "No description")</td>
                                    <td>@(asset.Brand ?? "N/A") / @(asset.Model ?? "N/A")</td>
                                    <td>
                                        @if (asset.Status == "Available")
                                        {
                                            <span class="badge bg-success">متاح</span>
                                        }
                                        else if (asset.Status == "Under Maintenance")
                                        {
                                            <span class="badge bg-warning text-dark">صيانة</span>
                                        }
                                        else if (asset.IsDisposed)
                                        {
                                            <span class="badge bg-danger">مكهن</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">@(asset.Status ?? "Unknown")</span>
                                        }
                                    </td>
                                   
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <hr class="my-4">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i> This user has no assigned assets.
                </div>
            }
            <!-- أزرار الإجراءات --> *@
            <div class="d-flex justify-content-between mt-4">
                <a class="btn btn-secondary" asp-action="DepartmentUsers">
                    <i class="bi bi-arrow-left"></i> العودة إلى القائمة
                </a>

                <div>
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                        <i class="bi bi-pencil-square"></i> تعديل المستخدم
                    </a>
                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger ms-2">
                        <i class="bi bi-trash"></i> حذف
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>