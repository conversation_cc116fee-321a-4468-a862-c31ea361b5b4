﻿
.reset-confirmation-container {
    max-width: 650px;
    width: 100%;
    margin: 0 auto;
}

.card {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    overflow: hidden;
}

.success-icon {
    animation: scaleUp 0.5s ease-in-out;
}

@@keyframes scaleUp {
    0% {
        transform: scale(0.5);
        opacity: 0;
    }

    70% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.alert-success {
    background-color: rgba(25, 135, 84, 0.1);
    border-color: rgba(25, 135, 84, 0.2);
    color: #0f5132;
    border-radius: 12px;
}

.btn-primary {
    background: linear-gradient(45deg, #2937f0, #9f1ae2);
    border: none;
    border-radius: 8px;
    transition: all 0.3s;
}

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
        background: linear-gradient(45deg, #1e2bd1, #8614c0);
    }

/* Card appearance animation */
.card {
    animation: fadeIn 0.8s ease-in-out;
}

@@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}


