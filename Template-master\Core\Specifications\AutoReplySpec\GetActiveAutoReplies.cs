using Core.Entities;

namespace Core.Specifications.AutoReplySpec
{
    public class GetActiveAutoReplies : Specification<AutoReply>
    {
        public GetActiveAutoReplies() : base(ar => ar.IsActive)
        {
            includes.Add(ar => ar.CreatedByUser);
            OrderByDesc = ar => ar.Priority;
        }

        public GetActiveAutoReplies(string trigger) : base(ar => ar.IsActive && ar.Trigger.Contains(trigger))
        {
            includes.Add(ar => ar.CreatedByUser);
            OrderByDesc = ar => ar.Priority;
        }
    }
}
