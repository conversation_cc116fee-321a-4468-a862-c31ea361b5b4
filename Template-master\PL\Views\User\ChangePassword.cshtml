﻿
@model ChangePasswordViewModel
@{
    ViewData["Title"] = "تغيير كلمة المرور";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-4" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-lg-7 col-md-9">
            <div class="card shadow-lg border-0 rounded-4">
                <div class="card-header bg-primary text-white p-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-key fa-2x me-3"></i>
                        <div>
                            <h3 class="mb-0">تغيير كلمة المرور</h3>
                            <p class="mb-0 text-white-50">قم بتحديث كلمة المرور الخاصة بك للحفاظ على أمان حسابك</p>
                        </div>
                    </div>
                </div>

                <div class="card-body p-4">
                    <div class="alert alert-warning mb-4">
                        <div class="d-flex">
                            <i class="fas fa-shield-alt fa-2x me-3 text-warning"></i>
                            <div>
                                <h5 class="alert-heading">نصائح لكلمة مرور قوية</h5>
                                <p class="mb-0">
                                    • استخدم 8 أحرف على الأقل<br>
                                    • استخدم مزيجًا من الأحرف الكبيرة والصغيرة<br>
                                    • أضف أرقامًا ورموزًا خاصة<br>
                                    • تجنب استخدام معلومات شخصية يسهل تخمينها
                                </p>
                            </div>
                        </div>
                    </div>

                    <form asp-action="ChangePassword" method="post" id="changePasswordForm">
                        <div class="form-group mb-4">
                            <label asp-for="CurrentPassword" class="form-label fw-bold">كلمة المرور الحالية <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="fas fa-lock text-primary"></i>
                                </span>
                                <input asp-for="CurrentPassword" class="form-control" type="password">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="CurrentPassword" class="text-danger small"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="NewPassword" class="form-label fw-bold">كلمة المرور الجديدة <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="fas fa-key text-primary"></i>
                                </span>
                                <input asp-for="NewPassword" class="form-control" type="password" id="newPassword">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="NewPassword" class="text-danger small"></span>

                            <!-- Password Strength Meter -->
                            <div class="password-strength mt-2 d-none" id="passwordStrength">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" role="progressbar" id="passwordStrengthBar" style="width: 0%"></div>
                                </div>
                                <div class="d-flex justify-content-between small mt-1">
                                    <span id="passwordStrengthText" class="text-muted">قم بإدخال كلمة المرور</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-4">
                            <label asp-for="ConfirmPassword" class="form-label fw-bold">تأكيد كلمة المرور الجديدة <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="fas fa-check-double text-primary"></i>
                                </span>
                                <input asp-for="ConfirmPassword" class="form-control" type="password" id="confirmPassword">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="ConfirmPassword" class="text-danger small"></span>
                            <div id="passwordMatch" class="small mt-1 d-none">
                                <i class="fas fa-check-circle text-success"></i>
                                <span class="text-success">كلمات المرور متطابقة</span>
                            </div>
                            <div id="passwordMismatch" class="small mt-1 d-none">
                                <i class="fas fa-times-circle text-danger"></i>
                                <span class="text-danger">كلمات المرور غير متطابقة</span>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4 pt-3 border-top">
                            <a asp-action="Profile" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitButton">
                                <i class="fas fa-check me-2"></i> تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $('.toggle-password').on('click', function() {
                const passwordField = $(this).closest('.input-group').find('input');
                const icon = $(this).find('i');

                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // Password strength meter
            $('#newPassword').on('input', function() {
                const password = $(this).val();
                $('#passwordStrength').removeClass('d-none');

                // Calculate password strength
                let strength = 0;
                let feedback = '';

                if (password.length === 0) {
                    strength = 0;
                    feedback = 'قم بإدخال كلمة المرور';
                } else if (password.length < 6) {
                    strength = 20;
                    feedback = 'ضعيفة جدًا';
                } else {
                    strength += 20; // Base strength for 6+ chars

                    // Add strength for length
                    if (password.length >= 8) strength += 10;
                    if (password.length >= 10) strength += 10;

                    // Add strength for character types
                    if (/[A-Z]/.test(password)) strength += 15;
                    if (/[a-z]/.test(password)) strength += 15;
                    if (/[0-9]/.test(password)) strength += 15;
                    if (/[^A-Za-z0-9]/.test(password)) strength += 15;

                    // Give feedback based on strength
                    if (strength < 40) {
                        feedback = 'ضعيفة';
                    } else if (strength < 70) {
                        feedback = 'متوسطة';
                    } else {
                        feedback = 'قوية';
                    }
                }

                // Update UI
                const bar = $('#passwordStrengthBar');
                bar.css('width', strength + '%');

                if (strength < 40) {
                    bar.removeClass('bg-warning bg-success').addClass('bg-danger');
                } else if (strength < 70) {
                    bar.removeClass('bg-danger bg-success').addClass('bg-warning');
                } else {
                    bar.removeClass('bg-danger bg-warning').addClass('bg-success');
                }

                $('#passwordStrengthText').text(feedback);
            });

            // Check if passwords match
            $('#confirmPassword').on('input', function() {
                const newPassword = $('#newPassword').val();
                const confirmPassword = $(this).val();

                if (confirmPassword.length > 0) {
                    if (newPassword === confirmPassword) {
                        $('#passwordMatch').removeClass('d-none');
                        $('#passwordMismatch').addClass('d-none');
                    } else {
                        $('#passwordMismatch').removeClass('d-none');
                        $('#passwordMatch').addClass('d-none');
                    }
                } else {
                    $('#passwordMatch, #passwordMismatch').addClass('d-none');
                }
            });

            // Form validation
            $('#changePasswordForm').on('submit', function(e) {
                const currentPassword = $('#CurrentPassword').val();
                const newPassword = $('#newPassword').val();
                const confirmPassword = $('#confirmPassword').val();

                if (!currentPassword || !newPassword || !confirmPassword) {
                    e.preventDefault();
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return false;
                }

                if (newPassword !== confirmPassword) {
                    e.preventDefault();
                    alert('كلمات المرور غير متطابقة');
                    return false;
                }

                return true;
            });
        });
    </script>

    <style>
        /* RTL specific adjustments */
        .me-1, .me-2, .me-3, .me-4 {
            margin-right: 0 !important;
        }

        .me-1 {
            margin-left: 0.25rem !important;
        }

        .me-2 {
            margin-left: 0.5rem !important;
        }

        .me-3 {
            margin-left: 1rem !important;
        }

        .me-4 {
            margin-left: 1.5rem !important;
        }

        /* Password field styling */
        .toggle-password {
            cursor: pointer;
        }

        .form-control:focus, .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        .input-group-text {
            border: 1px solid #ced4da;
        }

        .input-group:focus-within .input-group-text {
            border-color: #0d6efd;
        }
    </style>
}