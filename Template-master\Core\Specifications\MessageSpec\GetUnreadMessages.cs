using Core.Entities;

namespace Core.Specifications.MessageSpec
{
    public class GetUnreadMessages : Specification<Message>
    {
        public GetUnreadMessages() : base(m => !m.IsRead && !m.IsSent)
        {
            includes.Add(m => m.Chat);
            includes.Add(m => m.SentByUser);
            OrderByDesc = m => m.CreatedAt;
        }

        public GetUnreadMessages(int chatId) : base(m => m.ChatId == chatId && !m.IsRead && !m.IsSent)
        {
            includes.Add(m => m.Chat);
            includes.Add(m => m.SentByUser);
            OrderByDesc = m => m.CreatedAt;
        }

        public GetUnreadMessages(string assignedUserId) : base(m => !m.IsRead && !m.IsSent && m.Chat.AssignedUserId == assignedUserId)
        {
            includes.Add(m => m.Chat);
            includes.Add(m => m.SentByUser);
            OrderByDesc = m => m.CreatedAt;
        }
    }
}
