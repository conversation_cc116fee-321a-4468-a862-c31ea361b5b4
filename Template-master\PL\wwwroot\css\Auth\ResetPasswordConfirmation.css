﻿
.success-container {
    max-width: 650px;
    width: 100%;
    margin: 0 auto;
}

.card {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    overflow: hidden;
    animation: fadeInUp 0.8s ease-out;
}

@@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert-success {
    background-color: rgba(25, 135, 84, 0.1);
    border-color: rgba(25, 135, 84, 0.2);
    color: #0f5132;
    border-radius: 12px;
}

.btn-primary {
    background: linear-gradient(45deg, #2937f0, #9f1ae2);
    border: none;
    border-radius: 8px;
    transition: all 0.3s;
}

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
        background: linear-gradient(45deg, #1e2bd1, #8614c0);
    }

/* Checkmark Animation */
.success-animation {
    width: 100px;
    height: 100px;
    margin: 0 auto;
}

.checkmark-circle {
    width: 100px;
    height: 100px;
    position: relative;
    display: inline-block;
    vertical-align: top;
    background-color: #19875445;
    border-radius: 50%;
}

.checkmark {
    border-radius: 5px;
}

    .checkmark.draw:after {
        animation-delay: 100ms;
        animation-duration: 1s;
        animation-timing-function: ease;
        animation-name: checkmark;
        transform: scaleX(-1) rotate(135deg);
        animation-fill-mode: forwards;
        opacity: 0;
        content: '';
        width: 36px;
        height: 70px;
        border-right: 10px solid #19875460;
        border-top: 10px solid #19875460;
        border-radius: 2px;
        position: absolute;
        left: 30px;
        top: 5px;
    }

@@keyframes checkmark {
    0% {
        opacity: 0;
        height: 0;
        width: 0;
    }

    40% {
        opacity: 1;
        height: 70px;
        width: 10px;
    }

    70% {
        opacity: 1;
        height: 70px;
        width: 36px;
    }

    100% {
        opacity: 1;
        border-right: 10px solid #198754;
        border-top: 10px solid #198754;
    }
}

