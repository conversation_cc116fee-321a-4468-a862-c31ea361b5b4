using System.ComponentModel.DataAnnotations;

namespace Core.Entities
{
    public class ServiceNumber : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        [MaxLength(20)]
        public string ServiceType { get; set; } = "support"; // support, sales, technical, emergency

        [MaxLength(100)]
        public string? Department { get; set; }

        [MaxLength(100)]
        public string? WorkingHours { get; set; }

        public int DisplayOrder { get; set; } = 1;

        // Contact statistics
        public int ContactCount { get; set; } = 0;

        public DateTime? LastContactAt { get; set; }

        // Created by user
        public string? CreatedByUserId { get; set; }
        public virtual User? CreatedByUser { get; set; }

        // Assigned users for this service
        public virtual ICollection<User> AssignedUsers { get; set; } = new List<User>();
    }
}
