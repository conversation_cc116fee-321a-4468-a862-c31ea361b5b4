using Core.Entities;

namespace Core.Specifications.ServiceNumberSpec
{
    public class GetAllServiceNumbers : Specification<ServiceNumber>
    {
        public GetAllServiceNumbers() : base()
        {
            includes.Add(sn => sn.CreatedByUser);
            includes.Add(sn => sn.AssignedUsers);
            OrderBy = sn => sn.DisplayOrder;
        }

        public GetAllServiceNumbers(bool isActive) : base(sn => sn.IsActive == isActive)
        {
            includes.Add(sn => sn.CreatedByUser);
            includes.Add(sn => sn.AssignedUsers);
            OrderBy = sn => sn.DisplayOrder;
        }

        public GetAllServiceNumbers(string serviceType) : base(sn => sn.ServiceType == serviceType)
        {
            includes.Add(sn => sn.CreatedByUser);
            includes.Add(sn => sn.AssignedUsers);
            OrderBy = sn => sn.DisplayOrder;
        }

        public GetAllServiceNumbers(string serviceType, bool isActive) : base(sn => sn.ServiceType == serviceType && sn.IsActive == isActive)
        {
            includes.Add(sn => sn.CreatedByUser);
            includes.Add(sn => sn.AssignedUsers);
            OrderBy = sn => sn.DisplayOrder;
        }
    }
}
