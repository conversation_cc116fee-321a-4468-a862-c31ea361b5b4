{"GlobalPropertiesHash": "pJtnaMf7enlpOHoREP+HOYSzLt+rqIa3X7bsOBFTRw8=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["sYnbHgObiL5qMm6tLVg/KQUnErzfYFV61xbCCpawFds=", "O+SFNE0EOOuNRbyTs5feyvuAjKdoQQUHrXPggrdYrxo=", "pE81cR9pPIPGaaQjyhTWmgrbaxkzbCBMQ4ZBy281iVk=", "FJKiFUs2ni+xDBhstrR5TZDPSoVRilPVkaGgFol8+Zo=", "KKproFJREWmE3s2bJCOroP0YY9IsPpX8mOLyw/QaKfQ=", "+qRGehcho2rFXIGz4qbsOqLvo8ODRyv8a29sV6Atp9I=", "tpL1sTwdiiBsjgPprrtrMRTxec01nYEE3MUbOV9QcZY=", "K3Gp/hsnbAZfwJ0Vz+R5v35/Hxb3kqAtGICoAdSiDhA=", "imdZHWtYRvYDqV1tQ/nzk4swcRQayjsvA43IjaxjlyI=", "3dvr0SSBSwAauYLwI2AmTVxoLLYPbpBZFBZyNiScVwM=", "42Ww4/xjWxaymxQGKeHOV4FfmM5nd2OXfD+nKv804os=", "Q3Tej8CpARvZuhmtn5yYqcEITGkXzyyXl3dXnoNd1vw=", "/aIQN4oAMHfGUvyXlV41xTaSmPMfdAVuMpJJoIPsJLw=", "GCkmglZA1iLcRhgR2XtbVLRPPoCqo61q0hu8XZueX9w=", "DBDSxTEkEVpd7hN+vmKfCw9pMM5YD4LZUkWXDd085l4=", "WFCyOqYrqVKVIe+v8G6M/IxmIFLsyTxFJSRSirZccwI=", "2oFrrqrI0qSXQXQk+h5Zbx7c7eFLUIm7t2fSvigOC/I=", "sv31L/XAhwictJOjF+jmdzcukNvNLQkePHlzWLl9Z/s=", "Xgzf691eXdYa694yCKw8+83hfEULr1cjneXv+wcbHW8=", "cxH4h05wg6//IVxxdI8dLesRjlo/I/hxZtpqG9Z8Wls=", "vc64Nc4NRF+nwmEnA4PFpg4gKK1kmdEDN21gh8nEXkE=", "Fi0N073AOpYutM/BZoTSMH46WHffZVpiNAtT83G7y3I=", "ExJrB5HUDu4ea7FBshnUBdDN7D4QbK8WoqXdL/yC/Fc=", "2mcUJ5VfD5oSRoXdR63w8ZleejVXJFPwxAvT15oKDKY=", "k084SfV/ySUN99VAU+qY5GotLbWNSaTalDKX/+oxVYQ=", "vclfk2HWQz2U6nevtCf4eqqUz7jfKWssPOBQs6TlSQw=", "zKNTauQmnJnDKQu8KhePcnosdiYevST0fJdSe581HC4=", "8obIkGl8h2u5uja02p+iSEpOZ2Y0tSYvqtfPOaXORaU=", "j2oal4sjLIvOr5hsn+me3tnIP3yJPoOOMa3kdMRBxSc=", "CHkl2nQSuyrHZhOFFjNbUvWHG1TAneMwbEOvfB6ISUI=", "xQOGxfD9tvXo7JNZSNNShU0YiKRcDonw69YvpPIi6ys=", "P3f2iMFfAy+hlIRR2NdQlhhwsslcYzUElfhxAmIC0dE=", "sS4QWQt2LX29N7dDZOwvQVkjJYx8Hbd23ctpqy+UGzQ=", "9pxHQZg4q1MykBWR6KUDIUA8mdgZ+oT+5pnBQ+VHuno=", "SOLURMJA3OK1CuDnQrKeTEIWmzYJYemMVXpuafOBiu8=", "2iP3NxKt/vlaNulsGvuQF9/CgNu6Zw08hfrecjggjAQ=", "0Tjdunr0GVtNxyiNhvNsxEsy9ofKt0MHbrDk7YaCMlE=", "UzenRq7hAHSUfa32TGdPI8v5gwrVzaO49ukM3VLdEHo=", "IOoq7Zm4hI85xK8GivBujZYf2UydE7L7Pbz3YbtTV1o=", "iINDh45rro3JU28WSbht3W91F0K1S/73du5Qs9HUQ3M=", "kehE1d/6aJnBJIqq1veM7Z+5xwaCbYueKvUtl93mdiQ=", "VI5zxWF/YMpw7hPThAy5ihIZgXkwwAa1qZb+F+hhJS8=", "R+kWhCBUfYBfMIA/9Bj9WwIauUnUEI/jCeG3x5jE92Y=", "8wZqtpJ8qe3OgH1ePgbNV82KHn/iW+dpyGU3qo2YsgM=", "S+eRPK3OYxTVEdHcQ3M40GiLydedvQsM7u+MIMGfT7E=", "Of5dhdnUlHGSi9HOEeCge34vP2QS091YkcTWfxSdRIg=", "yax9bIb/hlcwgFwuDHM89GRcsb1FjK/3lIjuQxmKFEc=", "04Ffj4YOjoLUnGGJY0V1Pi8Ilwv1DMnZFZiJWO1FDVE=", "CRhyBvJbRkEXaOIqBbN9NXc3o/f/oH5dxp9eeTEaAWI=", "D9FCoSQYu3Eba4+rZu7WtpmdV6SNo3y59bUBWiQFcKk=", "Cb18+Q+IpGzUwQyCtOmCJj8OtE+SKLfIzlfO92pvHpo=", "qz2+L06Ql1zbcqrRps9zU7LxR5hATTnl/jRLnkwvAn8=", "5qArJQDCSO70d4BUi0cS8LEbj3nVswTizNTGoF5Vt3I=", "AMhySxolHSdvNsi7zOInFEWlyVxSNMVDzFBQk0g3RN8=", "bgJsXgR4eoPAZm1ps+s3LR1PG4fN3PGfScpBqyy/aQQ=", "60f33tclovgqdPhDEQBFSqzn9zDTbhuZpnBnzoGPVAM=", "Qjx2kXfJ8fUW50n54qJTsrELdQby6KIi/YIefKsJU1Y=", "a0vR7zQh0rUVbYaoNS5RxG4koNS0Yg1Yval0Xvzvy6c=", "vb9NSyCSir+CZ+SvHiMVcxh70cLR1ik3Isuq0rZ9epE=", "T5dgW+vU40C/s0f8mkqx4fxL8IcnLWUd77vyltJ40UM=", "bNHFUTv6uwkR6DJrJJarDp8rIEm6iQuxA674iAo82bg=", "ZZGdH8g9U/ZnbSswBogNGIQX5xcEa91PUzimMg3RtFA=", "tTBp/tcKn5Gv3RsWuvXFRaTM2RraoRUZEo3vk5/D+3c=", "Xbp9gweVofakE3G0q6lr+a3vuW/Df8SV3uok+/xT6nE=", "vi7/0lqsT9AF4ALfI7gbqA9qnFi7lAmV3vPH1d0fE+o=", "2L9fFTP7g/0WI7aGua2LMFMVva3FRCRDrcMQE+BYw2s=", "aAQh+HKTDEWN2OQUqXUbQzUFZmCtRQzwO4Gh+xGbfjs=", "gn8T/0U0xJ28uQ858XD1KqyENbc/nRjXht2Thj+H1CI=", "5UKRF3s9ol7Rg52pPYtp6d5yvjVU2uxgb7PKzn/NzTQ=", "HpUtgSfGg9YXU2W2/DLtqHGiQ2pcwgj3KQOwo0dLkFw=", "oLVgPM4ET39R805LEQ4g+AfM6MCMb/ZTVO5qYxEhi4w=", "rV7G7k39VUuoFe8aSgHVkp+LAsG7rxyi1skm8mUDnaA=", "Bf+9GnthQosY8mARXyB3XVkSICfbcy+6Yi/bPQ1jflY=", "gokmb6gBZ3Rae+zG1mwIjnzxWscxk1EchG2/fSs7JrY=", "9wvSjMCl50Hc2w405iaRA7YfWczggsWihOXJNgQbWYI=", "L4bCzjwOSzF9UUs8eh6DjTDPrBAFo+RaoXRsdnPrlcc=", "XgDAF0ffhUsXX67vl314FnozJ19bWvnCkTd3SLsjuZY=", "z7kFD3xZ3yszkex24SIk56K7l14HiI18kflMTRnXViE=", "kmsUtaYiPi45D+JV/+RDKLw/sQC3V6yXNMxzojiw2D8=", "1mj6rWvt++TPgsRNuS14g/3SzsC3K97jqhfaN4ngnII=", "9LjHsn/SHwgwDEj58GV7B5MmxgPktgfVcyN8qMpcisk=", "YbCIS/zc0g7GnIIHR1ZOT7nqmMYOAflYaM92p49nqic=", "de9M/CVGDPERThHfBQgihs5ziwYfXRHl1immpBtZylk=", "ZmOHPPytNe/9vH0ICrMPJ6hgaMZOkdopu2NC5kOpVIw=", "9WnnXDqW7n0u8BCPxTwHrXyvH620Pp+/WQ7muH4/ko4=", "TXhHnsjRyhfI94RgtBgNxNxiBzRg4/MDt4eJdLu12QY=", "lEXYcaeNys0sf8RLUlWC8ZRq0XC7LN9Nq6UL7phatsc=", "eLHvL0Qqj70norTOlC03eTb5pPzVkTdL7CVeoICDKzI=", "nFdjCtwmv5FpGhB6rk2rAgtpbfRv1NTUld05lMB0Xt8=", "qrtuZXQasxKI0o6zu5wciBaFno8SqqehGmiYe/aLAYY=", "p4wAtcpzZlUVE3xKzVkv1dp4BPl4tMa8j5tgM1SFZuE=", "Znz6eMDMojGvek+cVcZ7kR432x8c8r1Tj1P0h5T8O5U=", "f6LLBE9IUOTGeBSFE+iharP7tgGrM/f1cEPMD7RxmWs=", "KiCde0TpsWHPKkFTGe6wzRowlV+g7esqQPI849y20G0=", "RswPHk6a8ZZXMF7z9rl4bacWJYsBR0W/2uv1ci5rx5E=", "R2p6wKL7Kd+gCId0r3iwkArrk6wLZgMRgvGdQu1ULys=", "rX+dSfXPXL2Nv2pHvqDHapl1+9RIkQFeM6NKVzattFE=", "NZYEifC6HXUbMT+nSWHMpGSIyDOLHf0oU7lTaftyz0g=", "JEFsyaZNZL1uLAX4kLVUHukPKbazsktMGjYw2fbCrk8=", "6phWO3YzMbkdCqtq6xbyj+ikYxLoWWRDSAwPmS8Mq28=", "YmZU3FggwjKoyppdIX0FXpRbWJuqdz9twrxTZgUpjHs=", "CacwWJYI5wtSsTztCsY1UJyZYFNSZXWbYxfunNk1Y54=", "ATixHZgz3Jk0bRedMbJUyETw3dFrter02qDvmJJRqfc=", "9H6N29b48M6zFAAFBUwYIU3gPGBl8c4/85i+bl6NuPs=", "VegUBT/6DxfevCLIxbg1/qpL8rD1nDEJxEIHeOBF1As=", "tQ/RffgkALQbTuCgTaiX7/MJ8pgxa9HflvHVq94QAqQ=", "JEl1ubzaB3X7jqi6FLSe+RZSlOlwvm0VugRqUCL+jDs=", "RahXiWiuJZcrJiZoQZbaEud4BZXxfBRsVm2yHok2M8U=", "jUJdn22ZfITbSUHQjh4pPOpQlobkv0qaKI4U+lVDjjs=", "zA022vQ+hjB1am+3WyVH4hvHPUIvdGQx6cw6Kj4Y1N0=", "smO8efCmSm8ArmJyrX+9feDCr/0miC41GmWtHLg/fg4=", "S9v8Q0EXdxE/jjAc/AjVNi1U1R17KDuu3bEZJpCO6Y0=", "L4wyKYHBSXo10Oo96y9MMhfl/lUNkT/WPbyhFO3SzIk=", "EFs5R6TxEBlFSxp62qDJHvk3xpkn+wRJ4koR98kYnkw=", "5XWKgLG0tEsRKsnC6h7khUwhg/Z/YcHH3iHMg1LF8Bk=", "NqT5xZznHReJsyA8OBE3bRONleBCliwnKmC1mWaR5OE=", "Gp+G2GQa51J2rwPu/34Z/mr/Hqk0LizgDGT0uPoilDw=", "8S5QQfZJQNdaDQ5lDbkjQcPWsfk2NcRI905+uXajzEs=", "FcZRhB9ZzQhTzN8Il9a1RvgaeJh55Tsn0gzCfu6MiNU=", "P82Pd1AYYwImUv8OYjNcOjuVYt2cQ/FplsXA3f+Nk0w=", "FkwCOaX9I0XK4mSL6INpj2dPg0knQHsbUZmysWYby3I=", "+kjGNDxxI4b22oLWmvr78jhpQ26QyNHWodo6OsKITj4=", "G9Hu9vUjSKAY6pXIGNW3SvE5iunaOiKgg9ElB/1Oux8=", "TcmZBB9rR0qKuVAosAGRkIgclP/Nsfy+JI1vq2L+Pw8=", "dEvWqn+/ELp519v+phKeabVLx6tQOknor5If/8zNG5E=", "6jpGCOtVrkN+freOJ8LXB8PMHX+nH5kua76zoa5CnAA=", "dzHy6/wUUhHqp9YqN54+ibt8Ej1/ifqHovlV+K41uaU=", "k2FYuIBC9Vqdb2wZ9FinkAQdWUJ7KlNTDqC6OAT5qXQ="], "CachedAssets": {"42Ww4/xjWxaymxQGKeHOV4FfmM5nd2OXfD+nKv804os=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Layout.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/Layout#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "537<PERSON><PERSON><PERSON><PERSON>4", "Integrity": "YdDI1/JefDMlk0dCskjVrkvbFQnfMF/SEgznNLf4s8U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Auth\\Layout.css", "FileLength": 513, "LastWriteTime": "2025-08-23T18:26:16.0415025+00:00"}, "O+SFNE0EOOuNRbyTs5feyvuAjKdoQQUHrXPggrdYrxo=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "la7x5y3yhh", "Integrity": "07/3MwdHiZPHML50pgcpuTdz06LmSfeDIYlPoNAw+ow=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\adminlte.css.map", "FileLength": 2405822, "LastWriteTime": "2025-05-06T12:40:44.9015626+00:00"}, "pE81cR9pPIPGaaQjyhTWmgrbaxkzbCBMQ4ZBy281iVk=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.min.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "13ti5y8wq9", "Integrity": "vdB1Qr2ck9tU7BlOV1srXHsCWGNH6cvV7lbT4h8+gs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\adminlte.min.css", "FileLength": 1396758, "LastWriteTime": "2025-05-06T12:40:44.3725467+00:00"}, "FJKiFUs2ni+xDBhstrR5TZDPSoVRilPVkaGgFol8+Zo=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.min.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9ccuhph3ns", "Integrity": "+R7l1fmFcV0+ooHk2acqfnQpDTKzai3dw2lPRAvf+7g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\adminlte.min.css.map", "FileLength": 3846965, "LastWriteTime": "2025-05-06T12:40:45.8923245+00:00"}, "KKproFJREWmE3s2bJCOroP0YY9IsPpX8mOLyw/QaKfQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nf8rx9prei", "Integrity": "cziAiAL30DEwUV9f5zp1Kyw/sI36XoKG/JDXpisSfps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\adminlte.rtl.css", "FileLength": 373481, "LastWriteTime": "2025-05-06T12:40:42.7996961+00:00"}, "+qRGehcho2rFXIGz4qbsOqLvo8ODRyv8a29sV6Atp9I=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "a1v63x9wfv", "Integrity": "nnnLtiNfYYtFNT1h8oWxccJWsN+mveFArejYqMPNPBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\adminlte.rtl.css.map", "FileLength": 853328, "LastWriteTime": "2025-05-06T12:40:44.0154872+00:00"}, "tpL1sTwdiiBsjgPprrtrMRTxec01nYEE3MUbOV9QcZY=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.min.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "g954zfyzc2", "Integrity": "XI3V5TqHFl7tY8NIKga+ISkISZeNvVVxo1s8F4Ego3U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\adminlte.rtl.min.css", "FileLength": 297324, "LastWriteTime": "2025-05-06T12:40:42.7288859+00:00"}, "K3Gp/hsnbAZfwJ0Vz+R5v35/Hxb3kqAtGICoAdSiDhA=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.min.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q8ls3kvgdx", "Integrity": "iv2NjIGISJ1+wzh2894cKLrapmQLYjOy3oNkzdwOzMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\adminlte.rtl.min.css.map", "FileLength": 1115675, "LastWriteTime": "2025-05-06T12:40:44.5670273+00:00"}, "WFCyOqYrqVKVIe+v8G6M/IxmIFLsyTxFJSRSirZccwI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\dashbord\\dashbord.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/dashbord/dashbord#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qm74najt5r", "Integrity": "XrHoc0i9XQeFy3kjdUk/C6OBORJSAxLSWSNafTBFXZI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dashbord\\dashbord.css", "FileLength": 6332, "LastWriteTime": "2025-05-12T00:24:11.5822146+00:00"}, "sv31L/XAhwictJOjF+jmdzcukNvNLQkePHlzWLl9Z/s=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\site.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-08-23T11:03:21.6753164+00:00"}, "Fi0N073AOpYutM/BZoTSMH46WHffZVpiNAtT83G7y3I=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\favicon.ico", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-08-23T11:03:21.6633821+00:00"}, "ExJrB5HUDu4ea7FBshnUBdDN7D4QbK8WoqXdL/yC/Fc=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\img\\yos-removebg-preview.png", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "img/yos-removebg-preview#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gycisct6ti", "Integrity": "6FF2gXGVaxyQUnkYq8wwbJB4IQ8mIx0Pwm6JPEexrxU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\yos-removebg-preview.png", "FileLength": 156461, "LastWriteTime": "2025-08-23T11:58:59.0047432+00:00"}, "2mcUJ5VfD5oSRoXdR63w8ZleejVXJFPwxAvT15oKDKY=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/adminlte#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qacilm03ac", "Integrity": "whs2dExs3V4eC/d7DQTyRjnXWfPBg3OrEJR4JhfDvoE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\adminlte.js", "FileLength": 30170, "LastWriteTime": "2025-05-06T12:40:42.0302678+00:00"}, "k084SfV/ySUN99VAU+qY5GotLbWNSaTalDKX/+oxVYQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.js.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/adminlte.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ddky5r22iq", "Integrity": "X1ODiKw++BLGq+ORVDaLzkNvTjhoD5S+YoQBW5jAk5A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\adminlte.js.map", "FileLength": 46475, "LastWriteTime": "2025-04-27T21:45:33.5866551+00:00"}, "vclfk2HWQz2U6nevtCf4eqqUz7jfKWssPOBQs6TlSQw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.min.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/adminlte.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmxgv7s433", "Integrity": "uDZxtYgl1keAmJQ3+UmAlwbvNyTqpD/SKEf0Dj4bQNY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\adminlte.min.js", "FileLength": 11009, "LastWriteTime": "2025-05-06T12:40:42.0965711+00:00"}, "zKNTauQmnJnDKQu8KhePcnosdiYevST0fJdSe581HC4=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.min.js.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/adminlte.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3xv8oehij3", "Integrity": "aQxOagfCtHCJmLiDjzpPmiYfgipN04M+Zm5WejfQ93w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\adminlte.min.js.map", "FileLength": 37736, "LastWriteTime": "2025-04-27T21:45:33.5877524+00:00"}, "sS4QWQt2LX29N7dDZOwvQVkjJYx8Hbd23ctpqy+UGzQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Notification.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/Notification#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cszcmsjyt3", "Integrity": "1EnteHesam+c0VBUafqQyR9M8NmV5MZA6Cf4ze/WbEo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\Notification.js", "FileLength": 4141, "LastWriteTime": "2025-05-12T00:24:40.3044059+00:00"}, "9pxHQZg4q1MykBWR6KUDIUA8mdgZ+oT+5pnBQ+VHuno=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\site.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-08-23T11:03:21.6753164+00:00"}, "UzenRq7hAHSUfa32TGdPI8v5gwrVzaO49ukM3VLdEHo=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-08-23T11:03:21.487979+00:00"}, "IOoq7Zm4hI85xK8GivBujZYf2UydE7L7Pbz3YbtTV1o=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-08-23T11:03:21.4889284+00:00"}, "iINDh45rro3JU28WSbht3W91F0K1S/73du5Qs9HUQ3M=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-08-23T11:03:21.4898245+00:00"}, "kehE1d/6aJnBJIqq1veM7Z+5xwaCbYueKvUtl93mdiQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-08-23T11:03:21.4898245+00:00"}, "VI5zxWF/YMpw7hPThAy5ihIZgXkwwAa1qZb+F+hhJS8=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-08-23T11:03:21.4898245+00:00"}, "R+kWhCBUfYBfMIA/9Bj9WwIauUnUEI/jCeG3x5jE92Y=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-08-23T11:03:21.492166+00:00"}, "8wZqtpJ8qe3OgH1ePgbNV82KHn/iW+dpyGU3qo2YsgM=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-08-23T11:03:21.4926542+00:00"}, "S+eRPK3OYxTVEdHcQ3M40GiLydedvQsM7u+MIMGfT7E=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-08-23T11:03:21.493653+00:00"}, "Of5dhdnUlHGSi9HOEeCge34vP2QS091YkcTWfxSdRIg=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-08-23T11:03:21.493653+00:00"}, "yax9bIb/hlcwgFwuDHM89GRcsb1FjK/3lIjuQxmKFEc=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-08-23T11:03:21.4946506+00:00"}, "04Ffj4YOjoLUnGGJY0V1Pi8Ilwv1DMnZFZiJWO1FDVE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-08-23T11:03:21.4946506+00:00"}, "CRhyBvJbRkEXaOIqBbN9NXc3o/f/oH5dxp9eeTEaAWI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-08-23T11:03:21.4946506+00:00"}, "D9FCoSQYu3Eba4+rZu7WtpmdV6SNo3y59bUBWiQFcKk=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-08-23T11:03:21.4946506+00:00"}, "Cb18+Q+IpGzUwQyCtOmCJj8OtE+SKLfIzlfO92pvHpo=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-08-23T11:03:21.4961087+00:00"}, "qz2+L06Ql1zbcqrRps9zU7LxR5hATTnl/jRLnkwvAn8=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-08-23T11:03:21.4961087+00:00"}, "5qArJQDCSO70d4BUi0cS8LEbj3nVswTizNTGoF5Vt3I=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-08-23T11:03:21.4961087+00:00"}, "AMhySxolHSdvNsi7zOInFEWlyVxSNMVDzFBQk0g3RN8=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-08-23T11:03:21.4971776+00:00"}, "bgJsXgR4eoPAZm1ps+s3LR1PG4fN3PGfScpBqyy/aQQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-08-23T11:03:21.4981767+00:00"}, "60f33tclovgqdPhDEQBFSqzn9zDTbhuZpnBnzoGPVAM=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-08-23T11:03:21.4981767+00:00"}, "Qjx2kXfJ8fUW50n54qJTsrELdQby6KIi/YIefKsJU1Y=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-08-23T11:03:21.4997137+00:00"}, "a0vR7zQh0rUVbYaoNS5RxG4koNS0Yg1Yval0Xvzvy6c=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-08-23T11:03:21.4997137+00:00"}, "vb9NSyCSir+CZ+SvHiMVcxh70cLR1ik3Isuq0rZ9epE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-08-23T11:03:21.5007129+00:00"}, "T5dgW+vU40C/s0f8mkqx4fxL8IcnLWUd77vyltJ40UM=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-08-23T11:03:21.5007129+00:00"}, "bNHFUTv6uwkR6DJrJJarDp8rIEm6iQuxA674iAo82bg=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-08-23T11:03:21.5017107+00:00"}, "ZZGdH8g9U/ZnbSswBogNGIQX5xcEa91PUzimMg3RtFA=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-08-23T11:03:21.5027522+00:00"}, "tTBp/tcKn5Gv3RsWuvXFRaTM2RraoRUZEo3vk5/D+3c=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-08-23T11:03:21.5049691+00:00"}, "Xbp9gweVofakE3G0q6lr+a3vuW/Df8SV3uok+/xT6nE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-08-23T11:03:21.5059692+00:00"}, "vi7/0lqsT9AF4ALfI7gbqA9qnFi7lAmV3vPH1d0fE+o=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-08-23T11:03:21.50759+00:00"}, "2L9fFTP7g/0WI7aGua2LMFMVva3FRCRDrcMQE+BYw2s=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-08-23T11:03:21.5496277+00:00"}, "aAQh+HKTDEWN2OQUqXUbQzUFZmCtRQzwO4Gh+xGbfjs=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-08-23T11:03:21.5519741+00:00"}, "gn8T/0U0xJ28uQ858XD1KqyENbc/nRjXht2Thj+H1CI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-08-23T11:03:21.5519741+00:00"}, "5UKRF3s9ol7Rg52pPYtp6d5yvjVU2uxgb7PKzn/NzTQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-08-23T11:03:21.5533488+00:00"}, "HpUtgSfGg9YXU2W2/DLtqHGiQ2pcwgj3KQOwo0dLkFw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-08-23T11:03:21.5556538+00:00"}, "oLVgPM4ET39R805LEQ4g+AfM6MCMb/ZTVO5qYxEhi4w=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-08-23T11:03:21.5566886+00:00"}, "rV7G7k39VUuoFe8aSgHVkp+LAsG7rxyi1skm8mUDnaA=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-08-23T11:03:21.5576512+00:00"}, "Bf+9GnthQosY8mARXyB3XVkSICfbcy+6Yi/bPQ1jflY=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-08-23T11:03:21.558649+00:00"}, "gokmb6gBZ3Rae+zG1mwIjnzxWscxk1EchG2/fSs7JrY=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-08-23T11:03:21.5596459+00:00"}, "9wvSjMCl50Hc2w405iaRA7YfWczggsWihOXJNgQbWYI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-08-23T11:03:21.5606436+00:00"}, "L4bCzjwOSzF9UUs8eh6DjTDPrBAFo+RaoXRsdnPrlcc=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-08-23T11:03:21.5619869+00:00"}, "XgDAF0ffhUsXX67vl314FnozJ19bWvnCkTd3SLsjuZY=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-08-23T11:03:21.5619869+00:00"}, "z7kFD3xZ3yszkex24SIk56K7l14HiI18kflMTRnXViE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-08-23T11:03:21.562988+00:00"}, "kmsUtaYiPi45D+JV/+RDKLw/sQC3V6yXNMxzojiw2D8=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-08-23T11:03:21.5649817+00:00"}, "1mj6rWvt++TPgsRNuS14g/3SzsC3K97jqhfaN4ngnII=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-08-23T11:03:21.5649817+00:00"}, "9LjHsn/SHwgwDEj58GV7B5MmxgPktgfVcyN8qMpcisk=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-08-23T11:03:21.5659793+00:00"}, "YbCIS/zc0g7GnIIHR1ZOT7nqmMYOAflYaM92p49nqic=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-08-23T11:03:21.4898245+00:00"}, "de9M/CVGDPERThHfBQgihs5ziwYfXRHl1immpBtZylk=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-08-23T11:03:21.6653872+00:00"}, "ZmOHPPytNe/9vH0ICrMPJ6hgaMZOkdopu2NC5kOpVIw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-08-23T11:03:21.6663829+00:00"}, "9WnnXDqW7n0u8BCPxTwHrXyvH620Pp+/WQ7muH4/ko4=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-08-23T11:03:21.4926542+00:00"}, "TXhHnsjRyhfI94RgtBgNxNxiBzRg4/MDt4eJdLu12QY=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-08-23T11:03:21.5768095+00:00"}, "lEXYcaeNys0sf8RLUlWC8ZRq0XC7LN9Nq6UL7phatsc=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-08-23T11:03:21.6619098+00:00"}, "eLHvL0Qqj70norTOlC03eTb5pPzVkTdL7CVeoICDKzI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-08-23T11:03:21.6633821+00:00"}, "nFdjCtwmv5FpGhB6rk2rAgtpbfRv1NTUld05lMB0Xt8=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-08-23T11:03:21.6633821+00:00"}, "qrtuZXQasxKI0o6zu5wciBaFno8SqqehGmiYe/aLAYY=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-08-23T11:03:21.4926542+00:00"}, "p4wAtcpzZlUVE3xKzVkv1dp4BPl4tMa8j5tgM1SFZuE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-08-23T11:03:21.5679806+00:00"}, "Znz6eMDMojGvek+cVcZ7kR432x8c8r1Tj1P0h5T8O5U=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-08-23T11:03:21.5679806+00:00"}, "f6LLBE9IUOTGeBSFE+iharP7tgGrM/f1cEPMD7RxmWs=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-08-23T11:03:21.5709157+00:00"}, "KiCde0TpsWHPKkFTGe6wzRowlV+g7esqQPI849y20G0=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-08-23T11:03:21.5731119+00:00"}, "RswPHk6a8ZZXMF7z9rl4bacWJYsBR0W/2uv1ci5rx5E=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-08-23T11:03:21.5741119+00:00"}, "R2p6wKL7Kd+gCId0r3iwkArrk6wLZgMRgvGdQu1ULys=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-08-23T11:03:21.5761068+00:00"}, "rX+dSfXPXL2Nv2pHvqDHapl1+9RIkQFeM6NKVzattFE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-08-23T11:03:21.4910502+00:00"}, "imdZHWtYRvYDqV1tQ/nzk4swcRQayjsvA43IjaxjlyI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPassword.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/ForgotPassword#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "45cuv64g1r", "Integrity": "CNCStgTpCAzAfelHDIc2lhVzD+y8GZ2AifCSCDDzjQk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Auth\\ForgotPassword.css", "FileLength": 1420, "LastWriteTime": "2025-08-23T14:26:38.0102629+00:00"}, "3dvr0SSBSwAauYLwI2AmTVxoLLYPbpBZFBZyNiScVwM=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/ForgotPasswordConfirmation#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9vzcw40cgq", "Integrity": "aw/lfqyRhufDTfk3kFRyuC003/0/T5i67DCRWjSs19c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "FileLength": 1278, "LastWriteTime": "2025-08-23T14:46:34.0811439+00:00"}, "Q3Tej8CpARvZuhmtn5yYqcEITGkXzyyXl3dXnoNd1vw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Login.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/Login#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oqg7hy70n", "Integrity": "CRbUXZemvK0tZX5mRHKo8s6TITq/UETUbeixhGxfJWY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Auth\\Login.css", "FileLength": 2324, "LastWriteTime": "2025-08-23T14:48:11.5494092+00:00"}, "8obIkGl8h2u5uja02p+iSEpOZ2Y0tSYvqtfPOaXORaU=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Login.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/Auth/Login#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vk0m4z9v8p", "Integrity": "kRNYLg27u2PjVkjIPMBOa0l9xcPMxoib9imSEsnZC9M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\Auth\\Login.js", "FileLength": 867, "LastWriteTime": "2025-08-23T14:49:11.1450994+00:00"}, "2oFrrqrI0qSXQXQk+h5Zbx7c7eFLUIm7t2fSvigOC/I=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\home-dashboard.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/home-dashboard#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fmnrw6fry6", "Integrity": "V1jk550x96FsQWmonjScVfqM8VyVxkDlgpjHa6pWDWc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\home-dashboard.css", "FileLength": 7092, "LastWriteTime": "2025-08-23T16:24:43.3091531+00:00"}, "xQOGxfD9tvXo7JNZSNNShU0YiKRcDonw69YvpPIi6ys=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\home-dashboard.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/home-dashboard#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mw2y50nlgq", "Integrity": "Nhj2Hbu+iiIXU9eoQhq43jjpK/in/dY+S2LoH6KjRgc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\home-dashboard.js", "FileLength": 4812, "LastWriteTime": "2025-08-23T16:25:18.5985877+00:00"}, "P3f2iMFfAy+hlIRR2NdQlhhwsslcYzUElfhxAmIC0dE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\layout.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/layout#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uxltzax0eb", "Integrity": "Uo9LJt4V1tAO5vshSW8TPc44wmmNUcwo4fFweXAKhGE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\layout.js", "FileLength": 3409, "LastWriteTime": "2025-08-23T16:31:55.1354983+00:00"}, "cxH4h05wg6//IVxxdI8dLesRjlo/I/hxZtpqG9Z8Wls=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\user-index.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/user-index#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifja4tpxez", "Integrity": "3mhrPTAQBYpzofwez7pDwQQQhuozjEmARsZLiRHc5Lw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\user-index.css", "FileLength": 314, "LastWriteTime": "2025-08-23T16:33:06.3520375+00:00"}, "2iP3NxKt/vlaNulsGvuQF9/CgNu6Zw08hfrecjggjAQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\user-index.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/user-index#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fmtqv1f0zs", "Integrity": "dyuN7ppOwGk6LfWl9Z9UEfRzRM9NhyAIaX97Hvi+H4k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\user-index.js", "FileLength": 825, "LastWriteTime": "2025-08-23T16:34:57.3570456+00:00"}, "SOLURMJA3OK1CuDnQrKeTEIWmzYJYemMVXpuafOBiu8=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\user-edit-profile.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/user-edit-profile#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8se<PERSON>row<PERSON><PERSON>", "Integrity": "qHDExW0Roui6Q64xsOPYl7pE8lLykXuwNvZ0EhcjL0k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\user-edit-profile.js", "FileLength": 325, "LastWriteTime": "2025-08-23T16:36:50.0453981+00:00"}, "Xgzf691eXdYa694yCKw8+83hfEULr1cjneXv+wcbHW8=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\user-edit-profile.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/user-edit-profile#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ialj5o0ldp", "Integrity": "dkh2eCDOV6S5BAZibE3sxz0ytvxBFLbDGLfbE9/9ZzA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\user-edit-profile.css", "FileLength": 777, "LastWriteTime": "2025-08-23T16:37:02.2151417+00:00"}, "0Tjdunr0GVtNxyiNhvNsxEsy9ofKt0MHbrDk7YaCMlE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\user-profile.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/user-profile#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "08i07q0sn6", "Integrity": "wJTZO9mruTCBU0cR7vAWzR6sopHCGWtyADzxcZvTd9U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\user-profile.js", "FileLength": 1205, "LastWriteTime": "2025-08-23T16:38:40.4322551+00:00"}, "vc64Nc4NRF+nwmEnA4PFpg4gKK1kmdEDN21gh8nEXkE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\user-profile.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/user-profile#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bhzg9jivkj", "Integrity": "YTPaeTldKYHmqwaaweWGOD6Yl6q0Wpp+nY1OK6uqVOM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\user-profile.css", "FileLength": 467, "LastWriteTime": "2025-08-23T16:39:42.7929351+00:00"}, "DBDSxTEkEVpd7hN+vmKfCw9pMM5YD4LZUkWXDd085l4=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Rigester.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/Rigester#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j68zdcrzti", "Integrity": "K2HkJ1d4wylyzR2ot5dcn4WZ2+e8ItN7KxLqXHZsHmQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Auth\\Rigester.css", "FileLength": 1904, "LastWriteTime": "2025-08-23T18:28:46.1237748+00:00"}, "CHkl2nQSuyrHZhOFFjNbUvWHG1TAneMwbEOvfB6ISUI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Rigester.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/Auth/Rigester#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wms42xh9an", "Integrity": "+B+cVZN3P09PZJ1U1mqF9H75VoXQBgeB6NjtZPvxx3w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\Auth\\Rigester.js", "FileLength": 1000, "LastWriteTime": "2025-08-23T18:31:16.5973722+00:00"}, "sYnbHgObiL5qMm6tLVg/KQUnErzfYFV61xbCCpawFds=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b3s2zvn7in", "Integrity": "twmpJ8JTbbE4dDShorxelYdi03R4tYLyj/8MQClha8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\adminlte.css", "FileLength": 1612626, "LastWriteTime": "2025-08-23T17:28:16.0201001+00:00"}, "/aIQN4oAMHfGUvyXlV41xTaSmPMfdAVuMpJJoIPsJLw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPassword.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/ResetPassword#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "e44xbp7gr0", "Integrity": "4g2MMCm63keTIVOXn+5hMd6CX6LV9jx2kpUIlGjzyDw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Auth\\ResetPassword.css", "FileLength": 1872, "LastWriteTime": "2025-08-23T18:33:38.5026218+00:00"}, "GCkmglZA1iLcRhgR2XtbVLRPPoCqo61q0hu8XZueX9w=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/ResetPasswordConfirmation#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9mfsvlb7fx", "Integrity": "EaFqy/mUzSFClBVgN/WvNpMH/hW9hvy/wQ4bxfSuwH8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "FileLength": 2242, "LastWriteTime": "2025-08-23T18:37:24.2584485+00:00"}, "j2oal4sjLIvOr5hsn+me3tnIP3yJPoOOMa3kdMRBxSc=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\ResetPassword.js", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\", "BasePath": "_content/PL", "RelativePath": "js/Auth/ResetPassword#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pi47rgj3mu", "Integrity": "X8I1TtEvu6uOSlTz28YyjVMxsvWwLE1FOwLjOpq17v0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\Auth\\ResetPassword.js", "FileLength": 2766, "LastWriteTime": "2025-08-23T18:35:29.1822215+00:00"}}, "CachedCopyCandidates": {}}