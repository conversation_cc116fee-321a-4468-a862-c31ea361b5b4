using Core;
using Core.Entities;
using Core.Service.Contract;
using Core.Specifications.WhatsAppSettingsSpec;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Services
{
    public class WhatsAppSettingsService : IWhatsAppSettingsService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<WhatsAppSettingsService> _logger;

        public WhatsAppSettingsService(IUnitOfWork unitOfWork, ILogger<WhatsAppSettingsService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<IEnumerable<WhatsAppSettings>> GetAllSettingsAsync()
        {
            try
            {
                var spec = new GetAllSettings();
                return await _unitOfWork.GetRepository<WhatsAppSettings>().GetAllSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all settings");
                throw;
            }
        }

        public async Task<WhatsAppSettings?> GetSettingByKeyAsync(string key)
        {
            try
            {
                var spec = new GetSettingByKey(key);
                return await _unitOfWork.GetRepository<WhatsAppSettings>().GetByIdSpecAsync(spec);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting setting by key: {Key}", key);
                return null;
            }
        }

        public async Task<string?> GetSettingValueAsync(string key)
        {
            try
            {
                var setting = await GetSettingByKeyAsync(key);
                return setting?.SettingValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting setting value: {Key}", key);
                return null;
            }
        }

        public async Task<T?> GetSettingValueAsync<T>(string key)
        {
            try
            {
                var setting = await GetSettingByKeyAsync(key);
                if (setting == null) return default(T);

                return setting.DataType switch
                {
                    "boolean" => (T)(object)bool.Parse(setting.SettingValue),
                    "number" => (T)(object)int.Parse(setting.SettingValue),
                    "json" => JsonSerializer.Deserialize<T>(setting.SettingValue),
                    _ => (T)(object)setting.SettingValue
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting typed setting value: {Key}", key);
                return default(T);
            }
        }

        public async Task<bool> GetBooleanSettingAsync(string key, bool defaultValue = false)
        {
            try
            {
                var value = await GetSettingValueAsync(key);
                return bool.TryParse(value, out var result) ? result : defaultValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting boolean setting: {Key}", key);
                return defaultValue;
            }
        }

        public async Task<int> GetIntegerSettingAsync(string key, int defaultValue = 0)
        {
            try
            {
                var value = await GetSettingValueAsync(key);
                return int.TryParse(value, out var result) ? result : defaultValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting integer setting: {Key}", key);
                return defaultValue;
            }
        }

        public async Task<bool> SetSettingAsync(string key, string value, string? updatedByUserId = null, string? description = null)
        {
            try
            {
                var existingSetting = await GetSettingByKeyAsync(key);
                
                if (existingSetting != null)
                {
                    existingSetting.SettingValue = value;
                    existingSetting.UpdatedAt = DateTime.UtcNow;
                    existingSetting.UpdatedByUserId = updatedByUserId;
                    if (!string.IsNullOrEmpty(description))
                        existingSetting.Description = description;

                    _unitOfWork.GetRepository<WhatsAppSettings>().Update(existingSetting);
                }
                else
                {
                    var newSetting = new WhatsAppSettings
                    {
                        SettingKey = key,
                        SettingValue = value,
                        Description = description,
                        DataType = "string",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedByUserId = updatedByUserId
                    };

                    await _unitOfWork.GetRepository<WhatsAppSettings>().AddAsync(newSetting);
                }

                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Set setting: Key={Key}, Value={Value}", key, value);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting value: Key={Key}", key);
                return false;
            }
        }

        public async Task<bool> SetSettingAsync<T>(string key, T value, string? updatedByUserId = null, string? description = null)
        {
            try
            {
                string stringValue;
                string dataType;

                switch (value)
                {
                    case bool boolValue:
                        stringValue = boolValue.ToString().ToLower();
                        dataType = "boolean";
                        break;
                    case int intValue:
                        stringValue = intValue.ToString();
                        dataType = "number";
                        break;
                    case string strValue:
                        stringValue = strValue;
                        dataType = "string";
                        break;
                    default:
                        stringValue = JsonSerializer.Serialize(value);
                        dataType = "json";
                        break;
                }

                var existingSetting = await GetSettingByKeyAsync(key);
                
                if (existingSetting != null)
                {
                    existingSetting.SettingValue = stringValue;
                    existingSetting.DataType = dataType;
                    existingSetting.UpdatedAt = DateTime.UtcNow;
                    existingSetting.UpdatedByUserId = updatedByUserId;
                    if (!string.IsNullOrEmpty(description))
                        existingSetting.Description = description;

                    _unitOfWork.GetRepository<WhatsAppSettings>().Update(existingSetting);
                }
                else
                {
                    var newSetting = new WhatsAppSettings
                    {
                        SettingKey = key,
                        SettingValue = stringValue,
                        DataType = dataType,
                        Description = description,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedByUserId = updatedByUserId
                    };

                    await _unitOfWork.GetRepository<WhatsAppSettings>().AddAsync(newSetting);
                }

                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Set typed setting: Key={Key}, Type={Type}", key, dataType);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting typed value: Key={Key}", key);
                return false;
            }
        }

        public async Task<bool> DeleteSettingAsync(string key)
        {
            try
            {
                var setting = await GetSettingByKeyAsync(key);
                if (setting == null) return false;

                _unitOfWork.GetRepository<WhatsAppSettings>().Delete(setting);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Deleted setting: Key={Key}", key);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting setting: Key={Key}", key);
                return false;
            }
        }

        public async Task<bool> ToggleSettingStatusAsync(string key)
        {
            try
            {
                var setting = await GetSettingByKeyAsync(key);
                if (setting == null) return false;

                setting.IsActive = !setting.IsActive;
                setting.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.GetRepository<WhatsAppSettings>().Update(setting);
                await _unitOfWork.CompleteAsync();

                _logger.LogInformation("Toggled setting status: Key={Key}, IsActive={IsActive}", key, setting.IsActive);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling setting status: Key={Key}", key);
                return false;
            }
        }

        #region Predefined Settings

        public async Task<bool> IsAutoReplyEnabledAsync()
        {
            return await GetBooleanSettingAsync("auto_reply_enabled", true);
        }

        public async Task<bool> IsNotificationsEnabledAsync()
        {
            return await GetBooleanSettingAsync("notifications_enabled", true);
        }

        public async Task<bool> IsChatSavingEnabledAsync()
        {
            return await GetBooleanSettingAsync("chat_saving_enabled", true);
        }

        public async Task<bool> IsDarkModeEnabledAsync()
        {
            return await GetBooleanSettingAsync("dark_mode_enabled", false);
        }

        public async Task<bool> SetAutoReplyEnabledAsync(bool enabled, string? updatedByUserId = null)
        {
            return await SetSettingAsync("auto_reply_enabled", enabled, updatedByUserId, "Enable or disable automatic replies");
        }

        public async Task<bool> SetNotificationsEnabledAsync(bool enabled, string? updatedByUserId = null)
        {
            return await SetSettingAsync("notifications_enabled", enabled, updatedByUserId, "Enable or disable notifications");
        }

        public async Task<bool> SetChatSavingEnabledAsync(bool enabled, string? updatedByUserId = null)
        {
            return await SetSettingAsync("chat_saving_enabled", enabled, updatedByUserId, "Enable or disable chat history saving");
        }

        public async Task<bool> SetDarkModeEnabledAsync(bool enabled, string? updatedByUserId = null)
        {
            return await SetSettingAsync("dark_mode_enabled", enabled, updatedByUserId, "Enable or disable dark mode");
        }

        #endregion

        public async Task InitializeDefaultSettingsAsync()
        {
            try
            {
                var defaultSettings = new Dictionary<string, (object value, string description)>
                {
                    { "auto_reply_enabled", (true, "Enable or disable automatic replies") },
                    { "notifications_enabled", (true, "Enable or disable notifications") },
                    { "chat_saving_enabled", (true, "Enable or disable chat history saving") },
                    { "dark_mode_enabled", (false, "Enable or disable dark mode") },
                    { "max_messages_per_chat", (1000, "Maximum number of messages to keep per chat") },
                    { "auto_archive_days", (30, "Number of days after which inactive chats are archived") }
                };

                foreach (var setting in defaultSettings)
                {
                    var existingSetting = await GetSettingByKeyAsync(setting.Key);
                    if (existingSetting == null)
                    {
                        await SetSettingAsync(setting.Key, setting.Value.value, null, setting.Value.description);
                    }
                }

                _logger.LogInformation("Initialized default WhatsApp settings");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing default settings");
            }
        }
    }
}
