﻿@model IEnumerable<User>

@{
    ViewData["Title"] = "User Management";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- Bootstrap & DataTables Styles -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">

<!-- Custom Styles -->
@section Styles {
    <link href="~/css/user-index.css" rel="stylesheet" />
}
<div class="card mb-4">
    <div class="card-body">
        <h5 class="text-primary mb-3">Filter Users</h5>
        <div class="row">
            <div class="col-md-6">
                <a href="@Url.Action("DepartmentUsers", "User")" class="btn btn-primary">
                    <i class="bi bi-funnel"></i> Filter By Department
                </a>
            </div>
        </div>
    </div>
</div>
<!-- Page Content -->
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="text-primary">
            <i class="bi bi-people-fill me-2"></i> User Management
        </h2>
        <a asp-action="Create" class="btn btn-success">
            <i class="bi bi-person-plus-fill"></i> Add New User
        </a>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table id="userTable" class="table table-striped table-bordered align-middle">
                    <thead class="table-dark">
                        <tr>
                            <th>#</th>
                            <th>Full Name</th>
                            <th>Email</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model)
                        {
                            <tr>
                                <td>@user.Id</td>
                                <td>@user.FullName</td>
                                <td>@user.Email</td>
                                <td class="text-center">
                                    <a asp-action="Details" asp-route-id="@user.Id" class="btn btn-outline-info btn-sm me-1" title="View Details">
                                        <i class="bi bi-eye-fill"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@user.Id" class="btn btn-outline-warning btn-sm me-1" title="Edit User">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@user.Id" class="btn btn-outline-danger btn-sm" title="Delete User">
                                        <i class="bi bi-trash-fill"></i>
                                    </a>
                              
                                    <a asp-action="ManageRoles"
                                       asp-route-userId="@user.Id"
                                       class="btn btn-info btn-sm">
                                        <i class="fas fa-user-tag"></i> Manage Roles
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- jQuery & DataTables Scripts -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>

<!-- DataTable Initialization -->
<script src="~/js/user-index.js"></script>