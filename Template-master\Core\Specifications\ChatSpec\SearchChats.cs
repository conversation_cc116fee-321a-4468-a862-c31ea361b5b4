using Core.Entities;

namespace Core.Specifications.ChatSpec
{
    public class SearchChats : Specification<Chat>
    {
        public SearchChats(string searchTerm) : base(c => 
            c.CustomerName.Contains(searchTerm) || 
            c.CustomerPhone.Contains(searchTerm) || 
            (c.CustomerEmail != null && c.CustomerEmail.Contains(searchTerm)))
        {
            includes.Add(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1));
            includes.Add(c => c.AssignedUser);
            OrderByDesc = c => c.LastMessageAt;
        }

        public SearchChats(string searchTerm, bool includeArchived) : base(c => 
            (c.CustomerName.Contains(searchTerm) || 
             c.CustomerPhone.Contains(searchTerm) || 
             (c.CustomerEmail != null && c.CustomerEmail.Contains(searchTerm))) &&
            (includeArchived || !c.IsArchived))
        {
            includes.Add(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1));
            includes.Add(c => c.AssignedUser);
            OrderByDesc = c => c.LastMessageAt;
        }
    }
}
