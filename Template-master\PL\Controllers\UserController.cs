using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Security.Claims;
using Microsoft.AspNetCore.Mvc.Rendering;
using OfficeOpenXml;
using Core.Entities;
using Core.Service.Contract;
using Core.Utilities;
using PL.Models.user;

//[Authorize]
public class UserController : Controller
{
	private readonly UserManager<User> _userManager;
	
	private readonly IUserService _userService;
	private readonly RoleManager<IdentityRole> _roleManager;
    private readonly IWebHostEnvironment _env;

    public UserController(UserManager<User> userManager, IUserService userService, RoleManager<IdentityRole> roleManager, IWebHostEnvironment env)
	{
		_userManager = userManager;
		_userService = userService;
		_roleManager = roleManager;

        this._env = env;
    }

	[HttpGet]
	[Authorize] 
	public async Task<IActionResult> Profile()
	{
		
        var user = await _userManager.Users

			.FirstOrDefaultAsync(u => u.UserName == User.Identity.Name);

		if (user == null)
		{
			return NotFound();
		}

		return View(user);
	}

	public async Task<IActionResult> Details(string id)
	{
		var user = await _userManager.FindByIdAsync (id);
		if (user == null)
		{
			return NotFound();
		}

		// Get user roles
		var roles = await _userManager.GetRolesAsync(user);
		ViewBag.UserRoles = roles;

		return View(user);
	}



	// Create new user (GET)
	//[Authorize(Roles = Roles.Admin)]
	public async Task<IActionResult> Create()
	{
	
		var roles = new List<string>
	{
		Roles.Admin,
		Roles.Manager,
		Roles.Supervisor,
		Roles.User,
		Roles.DataEntry
	};
		ViewBag.AvailableRoles = new SelectList(roles);

		return View();
	}

	// Create new user (POST)
	[HttpPost]

	public async Task<IActionResult> Create(User user, string Password, string Role)
	{
		if (ModelState.IsValid)
		{
			user.UserName = user.FullName;
			
			// Create the user with the provided password
			var result = await _userManager.CreateAsync(user, Password);

			if (result.Succeeded)
			{
				// Assign the selected role
				if (!string.IsNullOrEmpty(Role))
				{
					await _userManager.AddToRoleAsync(user, Role);
				}

				return RedirectToAction(nameof(DepartmentUsers));
			}

			foreach (var error in result.Errors)
			{
				ModelState.AddModelError("", error.Description);
			}
		}

		// If we got this far, something failed, redisplay form
		//await PopulateDepartmentsDropdown(user.Department);

		// Repopulate roles dropdown
		var roles = new List<string>
	{
		Roles.Admin,
		Roles.Manager,
		Roles.Supervisor,
		Roles.User,
		Roles.DataEntry
	};
		ViewBag.AvailableRoles = new SelectList(roles);

		return View(user);
	}
	// Edit user (GET)
	//[Authorize(Roles = $"{Roles.Admin},{Roles.Manager}")]
	public async Task<IActionResult> Edit(string id)
	{
		var user = await _userManager.FindByIdAsync(id);
        if (user == null) return NotFound();

		// Populate departments dropdown
		//await PopulateDepartmentsDropdown(user.DepartmentId);

		return View(user);
	}

	// Edit user (POST)
	[HttpPost]
	//[ValidateAntiForgeryToken]
	public async Task<IActionResult> Edit(User user)
	{
		if (ModelState.IsValid)
		{
			var existingUser = await _userManager.FindByIdAsync(user.Id);
			if (existingUser == null)
			{
				return NotFound();
			}

			// Update user properties
			existingUser.FullName = user.FullName;
			existingUser.Email = user.Email;

			// Save changes
			await _userManager.UpdateAsync(existingUser);
            return RedirectToAction(nameof(DepartmentUsers));
		}

		// If we got this far, something failed, redisplay form
	//	await PopulateDepartmentsDropdown(user.DepartmentId);

		return View(user);
	}

	// Delete user (GET)
	//[Authorize(Roles = Roles.Admin)]
	public async Task<IActionResult> Delete(string id)
	{
		var user = await _userManager.FindByIdAsync(id);
        if (user == null) return NotFound();
		return View(user);
	}

	// Delete user (POST)
	[HttpPost]
	
	public async Task<IActionResult> DeleteConfirmed(string id)
	{
		await _userManager.DeleteAsync(await _userManager.FindByIdAsync(id));
        return RedirectToAction(nameof(DepartmentUsers));
	}

	[HttpGet]
	public async Task<IActionResult> EditProfile()
	{
		try
		{
			var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
			var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
				return NotFound();

			// Populate the department dropdown
			//await PopulateDepartmentsDropdown(user.DepartmentId);

			var model = new EditProfileViewModel
			{
				FullName = user.FullName,
				Email = user.Email,
	
			};

			return View(model);
		}
		catch (Exception ex)
		{
			Console.WriteLine($"Error in EditProfile (GET): {ex.Message}");
			return RedirectToAction("Error", "Home");
		}
	}


	[HttpPost]
	//[ValidateAntiForgeryToken]
	public async Task<IActionResult> EditProfile(EditProfileViewModel model)
	{
		if (!ModelState.IsValid)
		{
			//await PopulateDepartmentsDropdown(model.DepartmentId);
			return View(model);
		}

		try
		{
			var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
			var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
				return NotFound();

			user.FullName = model.FullName;
			user.Email = model.Email;
			
			var result = await _userService.UpdateUserProfileAsync(user);
			if (result)
			{
				TempData["Success"] = "Profile updated successfully";
				return RedirectToAction("Profile");
			}

			ModelState.AddModelError("", "Failed to update profile");
		}
		catch (DbUpdateException dbEx)
		{
			ModelState.AddModelError("", "Database error occurred while updating profile");
			Console.WriteLine($"Database Error: {dbEx.Message}");
		}
		catch (Exception ex)
		{
			ModelState.AddModelError("", "An unexpected error occurred while updating profile");
			Console.WriteLine($"Unexpected Error: {ex.Message}");
		}

		//await PopulateDepartmentsDropdown(model.DepartmentId);
		return View(model);
	}

	[HttpGet]
	public IActionResult ChangePassword()
	{
		return View();
	}

	[HttpPost]
	public async Task<IActionResult> ChangePassword(ChangePasswordViewModel model)
	{
		if (!ModelState.IsValid)
			return View(model);

		var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
		var result = await _userService.ChangePasswordAsync(userId, model.CurrentPassword, model.NewPassword);

		if (result)
			return RedirectToAction("Profile");

		ModelState.AddModelError("", "Password change failed");
		return View(model);
	}

	//[Authorize(Roles = Roles.Admin)]
	public async Task<IActionResult> ManageRoles(string userId)
	{
		var user = await _userManager.FindByIdAsync(userId);
		if (user == null)
		{
			return NotFound();
		}

		var model = new UserRoleViewModel
		{
			UserId = user.Id,
			Email = user.Email,
			FullName = user.FullName,
			CurrentRoles = (await _userManager.GetRolesAsync(user)).ToList(),
			AvailableRoles = _roleManager.Roles.Where(us => us.Name !="Data Entry").Select(r => r.Name).ToList()
		};

		return View(model);
	}

	[HttpPost]
	//[Authorize(Roles = Roles.Admin)]
	public async Task<IActionResult> UpdateRoles(string userId, List<string> selectedRoles)
	{
		var user = await _userManager.FindByIdAsync(userId);
		if (user == null)
		{
			return NotFound();
		}

		var currentRoles = await _userManager.GetRolesAsync(user);

		// Remove existing roles
		await _userManager.RemoveFromRolesAsync(user, currentRoles);

		// Add selected roles
		if (selectedRoles != null && selectedRoles.Any())
		{
			await _userManager.AddToRolesAsync(user, selectedRoles);
		}

		return RedirectToAction(nameof(DepartmentUsers));
	}

	[HttpGet]
	//[Authorize(Roles = $"{Roles.Admin},{Roles.Manager}")]
	public async Task<IActionResult> DepartmentUsers(string? searchTerm, string? roleName, int page = 1)
	{
        // Set up pagination
        int pageSize = 10;
        int skip = (page - 1) * pageSize;

        var roles = await _roleManager.Roles.ToListAsync();
        ViewBag.Roles = new SelectList(roles, "Name", "Name", roleName);

        // Get base query of all users
        var usersQuery = _userManager.Users
            .AsQueryable();



        // Apply search term filter if provided
        if (!string.IsNullOrEmpty(searchTerm))
        {
            searchTerm = searchTerm.Trim().ToLower();
            usersQuery = usersQuery.Where(u =>
                u.FullName.ToLower().Contains(searchTerm) ||
                u.Email.ToLower().Contains(searchTerm)
            );
            ViewBag.SearchTerm = searchTerm;
        }

        // Get total count before applying role filter (which requires in-memory processing)
        var totalCount = await usersQuery.CountAsync();

        // Get all users that match the current filters
        var filteredUsers = await usersQuery.ToListAsync();

        // Apply role filter if provided (this must be done in memory since roles are in a separate table)
        // Apply role filter if provided (this must be done in memory since roles are in a separate table)
        if (!string.IsNullOrEmpty(roleName))
        {
            var usersInRole = new List<User>();
            foreach (var user in filteredUsers)
            {
                // Check if user is in the selected role
                if (await _userManager.IsInRoleAsync(user, roleName))
                {
                    usersInRole.Add(user);
                }
            }
            filteredUsers = usersInRole;

            // Update total count after role filtering
            totalCount = filteredUsers.Count;

            // Get selected role name for display
            ViewBag.SelectedroleName = roleName;
            ViewBag.SelectedRoleName = roleName;
        }

        // Apply pagination
        var paginatedUsers = filteredUsers
            .Skip(skip)
            .Take(pageSize)
            .ToList();
        // Get roles for each user
        var userRoles = new Dictionary<string, List<string>>();
        foreach (var user in paginatedUsers)
        {
            var _roles = await _userManager.GetRolesAsync(user);
            userRoles[user.Id] = _roles.ToList();
        }

        // Add the roles to ViewBag
        ViewBag.UserRoles = userRoles;
        // Set pagination info for the view
        ViewBag.CurrentPage = page;
        ViewBag.PageSize = pageSize;
        ViewBag.TotalCount = totalCount;
        ViewBag.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

        return View(paginatedUsers);
    }

    [HttpPost]
  //  [Authorize(Roles = Roles.Admin)]
    public async Task<IActionResult> ApproveUser(string id)
    {
        var user = await _userManager.FindByIdAsync(id);
        if (user == null)
        {
            return NotFound();
        }
        await _userManager.UpdateAsync(user);
        TempData["Success"] = "تمت الموافقة على المستخدم بنجاح.";
        return RedirectToAction(nameof(DepartmentUsers));
    }

    [HttpPost]
   // [Authorize(Roles = Roles.Admin)]
    public async Task<IActionResult> ApproveUserAjax(string id)
    {
        var user = await _userManager.FindByIdAsync(id);
        if (user == null)
            return NotFound();

        await _userManager.UpdateAsync(user);

        // Get roles for this user
        var roles = await _userManager.GetRolesAsync(user);
        var userRoles = new Dictionary<string, List<string>> { { user.Id, roles.ToList() } };
        ViewBag.UserRoles = userRoles;

        return PartialView("_DepartmentUserRow", user);
    }


    [HttpGet]
   // [Authorize(Roles = $"{Roles.Admin},{Roles.Manager}")]
    public async Task<IActionResult> DownloadUserTemplate()
    {
        var sourcePath = Path.Combine(_env.WebRootPath, "ExcelTemplates", "UserImportTemplate.xlsx");

        if (!System.IO.File.Exists(sourcePath))
            return NotFound("Template file not found.");

        // Create a memory stream to hold a copy of the file
        using var sourceStream = new FileStream(sourcePath, FileMode.Open, FileAccess.Read, FileShare.Read);
        var memory = new MemoryStream();
        await sourceStream.CopyToAsync(memory);
        memory.Position = 0;

        return File(memory,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "UserImportTemplate.xlsx");


    }
    [HttpPost]
  //  [Authorize(Roles = $"{Roles.Admin},{Roles.Manager}")]
    public async Task<IActionResult> ImportUsers(IFormFile file)
    {
        if (file == null || file.Length == 0)
        {
            TempData["Error"] = "Please upload a valid Excel file.";
            return RedirectToAction("DepartmentUsers");
        }

        List<User> usersToAdd = new List<User>();
        HashSet<string> errors = new HashSet<string>();
        int successCount = 0;

        try
        {
        

            ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;

            using (var stream = new MemoryStream())
            {
                file.CopyTo(stream);
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets[0];
                    if (worksheet == null)
                    {
                        TempData["Error"] = "Invalid Excel file.";
                        return RedirectToAction("DepartmentUsers");
                    }

                    int rowCount = worksheet.Dimension.Rows;

                    // Skip header row
                    for (int row = 2; row <= rowCount; row++)
                    {
                        try
                        {
                            int c = 1;
                            string email = worksheet.Cells[row, c++].Value?.ToString();
                            string fullName = worksheet.Cells[row, c++].Value?.ToString();
                            string password = worksheet.Cells[row, c++].Value?.ToString() ?? "Password123!"; // Default password if none provided
                            string roles = worksheet.Cells[row, c++].Value?.ToString();



                            string nationalId = worksheet.Cells[row, c++].Value?.ToString();
                            string fileNumber = worksheet.Cells[row, c++].Value?.ToString();
                            

                            // Validate required fields
                            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(fullName))
                            {
                                errors.Add($"Row {row}: Email and Full Name are required.");
                                continue;
                            }

                            // Check if user already exists
                            var existingUser = await _userManager.FindByEmailAsync(email);
                            if (existingUser != null)
                            {
                                errors.Add($"Row {row}: User with email {email} already exists.");
                                continue;
                            }

                         

                            // Create new user
                            var user = new User
                            {
                                Email = email,
                                UserName = fullName,
                                FullName = fullName,
                                EmailConfirmed = true
                                
                                
                            };

                            // Create user in database
                            var result = await _userManager.CreateAsync(user, password);

                            if (result.Succeeded)
                            {
                                // Assign roles if provided
                                if (!string.IsNullOrEmpty(roles))
                                {
                                    var roleList = roles.Split(',').Select(r => r.Trim());
                                    string[] priority = { "Admin", "Manager", "Supervisor", "User" };

                                    foreach (var role in priority)
									{
                                        if (roleList.Contains(role))
                                        {
                                            await _userManager.AddToRoleAsync(user, role);
											break;
                                        }
                                    }
                                }
                                else
                                {
                                    // Assign default User role
                                    await _userManager.AddToRoleAsync(user, Roles.User);
                                }

                                successCount++;
                            }
                            else
                            {
                                errors.Add($"Row {row}: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                            }
                        }
                        catch (Exception ex)
                        {
                            errors.Add($"Row {row}: {ex.Message}");
                        }
                    }
                }
            }

            if (successCount > 0)
            {
                TempData["Success"] = $"Successfully imported {successCount} users.";
            }

            if (errors.Any())
            {
                TempData["ImportErrors"] = string.Join("<br>", errors);
            }
        }
        catch (Exception ex)
        {
            TempData["Error"] = $"Error importing users: {ex.Message}";
        }

        return RedirectToAction("DepartmentUsers");
    }
}