﻿
@model User

@{
    ViewData["Title"] = "Delete User";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- Bootstrap Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">

<!-- Custom Styles -->
<style>
    .center-container {
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
        width: 400px; /* يتحكم في عرض الكارد */
    }

    .btn:hover {
        transform: scale(1.05);
        transition: 0.3s ease-in-out;
    }

    .text-danger {
        font-weight: bold;
    }
</style>

<!-- Page Content -->
<div class="center-container">
    <div class="card shadow-sm border-danger text-center">
        <div class="card-body">
            <h2 class="text-danger mb-4">
                <i class="bi bi-trash3-fill me-2"></i> حذف المستخدم
            </h2>

            <p class="fs-5">
               هل انت متأمد من حذف <strong class="text-dark">@Model.FullName</strong>؟
            </p>

            <form asp-action="DeleteConfirmed">
                <input type="hidden" asp-for="Id" />

                <button type="submit" class="btn btn-danger me-2">
                    <i class="bi bi-trash"></i> حذف
                </button>

                <a asp-action="DepartmentUsers" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> عودة
                </a>
            </form>
        </div>
    </div>
</div>
