﻿.btn-outline-danger {
    border: 1px solid #dc3545; /* Red border */
    color: #dc3545; /* Red text */
    background-color: transparent; /* Transparent background */
    transition: all 0.3s ease; /* Smooth transition for hover effects */
    padding: 6px 12px; /* Padding for better spacing */
    border-radius: 4px; /* Rounded corners */
    font-size: 0.9rem; /* Slightly smaller font size */
}

    .btn-outline-danger:hover {
        background-color: #dc3545; /* Red background on hover */
        color: #fff; /* White text on hover */
        border-color: #dc3545; /* Red border on hover */
    }

    .btn-outline-danger i {
        margin-right: 5px; /* Space between icon and text */
    }
/* Set Arabic font family */
body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    background-color: #f8f9fa; /* Light background for better contrast */
}

/* Sidebar Styles */
.app-sidebar {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 250px;
    z-index: 1030;
    overflow-y: auto; /* Make sidebar scrollable */
    background-color: #343a40;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
}

.sidebar-brand {
    padding: 20px;
    background-color: #2c3136;
    border-bottom: 1px solid #4b545c;
    display: flex;
    align-items: center; /* Vertically center the content */
    justify-content: center; /* Horizontally center the content */
}

.brand-link {
    color: #fff !important;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.sidebar-brand .brand-link i {
    font-size: 1.5rem;
}

body.sidebar-collapse .app-sidebar {
    right: -250px;
}

body.sidebar-collapse .content-wrapper,
body.sidebar-collapse .main-header {
    margin-right: 0;
}

.pushmenu-btn {
    z-index: 1032;
    position: relative;
}

/* Sidebar Menu Styles */
.nav-sidebar {
    padding: 10px 0;
}



.nav-sidebar .nav-link {
    padding: 12px 20px;
    color: #c2c7d0;
    transition: background-color 0.3s ease, color 0.3s ease;
    border-radius: 4px;
    margin: 4px 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

    .nav-sidebar .nav-link:hover {
        background-color: #4b545c;
        color: #fff;
    }

    .nav-sidebar .nav-link.active {
        background-color: #007bff;
        color: #fff;
    }

.nav-sidebar .nav-icon {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* User Panel Styles */
.user-panel {
    padding: 15px;
    border-bottom: 1px solid #4b545c;
    background-color: #2c3136;
}

    .user-panel .info {
        color: #fff;
    }

        .user-panel .info small {
            color: #c2c7d0;
            font-size: 0.9rem;
        }

/* Navbar Styles */
.main-header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 1031;
    position: relative;
    transition: margin-right 0.3s ease;
}

.navbar-nav .nav-link {
    color: #343a40;
    padding: 10px 15px;
    transition: color 0.3s ease;
    position: relative;
}

    .navbar-nav .nav-link:hover {
        color: #007bff;
    }

/* Notification Badge */
.icon-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-width: 1.5rem;
    text-align: center;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* Notification Dropdown Styles */
.notification-dropdown {
    width: 350px;
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #ddd;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 0;
}

.notification-item {
    padding: 12px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.3s ease;
    position: relative;
}

    .notification-item:hover {
        background-color: #f8f9fa;
    }

    .notification-item.unread {
        border-right: 3px solid #007bff;
        background-color: #f0f7ff;
    }

    .notification-item h6 {
        font-weight: 600;
        margin-bottom: 5px;
        padding-left: 60px; /* Make room for buttons */
    }

    .notification-item p {
        margin-bottom: 5px;
        white-space: normal;
        word-wrap: break-word;
        overflow-wrap: break-word;
        padding-left: 60px; /* Make room for buttons */
    }

    .notification-item small {
        color: #6c757d;
        display: block;
        padding-left: 60px; /* Make room for buttons */
    }

    .notification-item .notification-actions {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .notification-item .btn {
        padding: 4px 8px;
        font-size: 0.8rem;
    }

.dropdown-header {
    background-color: #f8f9fa;
    font-weight: 600;
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
}

/* Mobile optimization */
@media (max-width: 576px) {
    .notification-dropdown {
        width: 300px;
    }

    .notification-item h6 {
        font-size: 0.9rem;
    }

    .notification-item p {
        font-size: 0.8rem;
    }
}

/* Content Wrapper Styles */
.content-wrapper {
    margin-right: 250px;
    margin-left: 0;
    padding: 20px;
    background-color: #fff;
    transition: margin-right 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Dark Mode Styles */
.dark-mode .main-header {
    background-color: #343a40 !important;
    border-color: #454d55 !important;
}

.dark-mode .navbar-nav .nav-link {
    color: #fff !important;
}

.dark-mode .content-wrapper {
    background-color: #454d55 !important;
    color: #fff !important;
}

.dark-mode .dropdown-menu {
    background-color: #343a40 !important;
    border-color: #454d55 !important;
}

.dark-mode .dropdown-item {
    color: #fff !important;
}

    .dark-mode .dropdown-item:hover {
        background-color: #454d55 !important;
    }
