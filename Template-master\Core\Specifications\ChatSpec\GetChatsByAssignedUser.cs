using Core.Entities;

namespace Core.Specifications.ChatSpec
{
    public class GetChatsByAssignedUser : Specification<Chat>
    {
        public GetChatsByAssignedUser(string userId) : base(c => c.AssignedUserId == userId)
        {
            includes.Add(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1));
            includes.Add(c => c.AssignedUser);
            OrderByDesc = c => c.LastMessageAt;
        }

        public GetChatsByAssignedUser(string userId, string status) : base(c => c.AssignedUserId == userId && c.Status == status)
        {
            includes.Add(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1));
            includes.Add(c => c.AssignedUser);
            OrderByDesc = c => c.LastMessageAt;
        }

        public GetChatsByAssignedUser(string userId, bool includeArchived) : base(c => c.AssignedUserId == userId && (includeArchived || !c.IsArchived))
        {
            includes.Add(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1));
            includes.Add(c => c.AssignedUser);
            OrderByDesc = c => c.LastMessageAt;
        }
    }
}
