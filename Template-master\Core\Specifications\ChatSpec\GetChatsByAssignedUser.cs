using Core.Entities;

namespace Core.Specifications.ChatSpec
{
    public class GetChatsByAssignedUser : Specification<Chat>
    {
        public GetChatsByAssignedUser(string userId) : base(c => c.AssignedUserId == userId)
        {
            includes.Add(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1));
            includes.Add(c => c.AssignedUser);
            OrderByDesc = c => c.LastMessageAt;
        }

    }
}
