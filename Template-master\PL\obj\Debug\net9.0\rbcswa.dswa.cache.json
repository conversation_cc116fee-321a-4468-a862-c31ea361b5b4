{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["sjKhI4h2MuDUKrYpwE9pQ52R96IEZWY5qyMBDJCxztE=", "B/YzT5lWdgrwle8SmC9TaZNwNrcyzuCgwlg7RLeYcxU=", "Om/EFo0w+A3YWA15EYEiS/vCUNaWQHGSS5SiOYlNIvk=", "kn8nvFPV03qEdwExyjM9YBWLvvZZ/4FFqOCnaSQFs98=", "/m9admfEScF0Gc/NDWKiZHJlJhGZJsBJxzREnHHsG/M=", "XOID14bq2l0wziWGJgQTq6hKs3+uedC3XFuaFEbRS3Y=", "9zVasDxcwgDw2/5VMH2YTjpln7AIkGrzMNRr9aADp8o=", "b3Nn1ZOWtt5z1jS7EbEiTlPvDkCVIwZ+1txggoXSRDg=", "6byYB8JYZg11sD2DkjxZkPa1g8CjtWwAV56uJz6d4iQ=", "jb2SuEUv9iv2MagZcdwlFd3inDXl6E3CRJFfj3yptPI=", "nMhRn2OKOQFBQ+u7JJCc/9tBWaTuW5Q+MW+i3ucUVZA=", "+Pvve9wtdrW+U9VeMu/4QZvB9M05BU7MQ4jzKWr81iw=", "KBXNBticn2Ib83XYHm0Yr6h3GUjaT3A1rXJw7T0YLYY=", "DQrJhOaia4hOBm5GJ4R9AfGl4Br2cYlHx0D8n7uGfzg=", "xwc1TyIltwaQpHf4S6VwugfJ7saF8a/6lOuozkYuOVY=", "is1Axy/JmkDx+9MRKVr4NxXQE4shKlQkxkqDCvnSKaw=", "h9sOYCf4WIZAEV6e1Yv6Bd+Z0/Ydlt+H7BhO1mXVX84=", "ck+8WOcWzV76/BI2ZaFce75Wl1UqarGzr/jzskC8WHw=", "q5RUBI+teJ4SPhxLmVwpK9ffwC6K1FUwwgW0sNZX/34=", "Rr4FE4zG7AYs3AB5/zZ2qwaCgiHgpOYaU7SEL/Q+ZTI=", "Z9bCZ3NQbQFDhXlH39nB2/XAMvUxyAJUYxwiPKQRrNc=", "7RFmxeUxFgVN5X/i4amO0bPWFcGzakeYX7vZ5ht2kDQ=", "99y6es7jHBhlJvwUHzTRpWoooZ//4vl+YGEFff5q3ds=", "w1V8ZbOLh2G5zNrPGbz5VexwCZ9npgQ1eEiJoXYDpyw=", "/Gr8Io7HICETrWjhZHQ95bn2W2V/pDDmPKFqcNgSaRQ=", "oBj+9+KJhQzndPtc9vaI3Ji8MtOM1AbOdLj8miNw0Xw=", "7o0ZuZ1cSB70eZX/804W7QBmdojcpODysgY3UFjIoWM=", "cDlVbJ6RJC3CB4CtlzZ49gZrmLSk07nFX7jVbc4p8jI=", "3srLWQf8+mTb67fXtgmDUjlPxQKjTfb9148YhMLpAVg=", "yMLciXalY7+qz3T8O49LA1qOtrQ0yuexLgrxveNLW0k=", "TW4Ig4EbhaYR0P/B4Ayvoqoj3Y6qd041iI7NQySpCyE=", "d5lRVdLv1YwwCgL+y0HGcmitnkyBTJ5B9VPL1g8H22Q=", "lDDKlX/D+i+gzvUInXzIyN6fKPgq019rnG1TvH7tuTA=", "/PZ4GiEh34bWS1VOgQLO6M6X3m5rpOthSogqYuCe8o4=", "V9HQqqV5HXpvD7SnYVf4lcUB6qCNx3N0Mu2GakOMfLo=", "DjYUoLHxTVeULejwjHJexhpXuIYoQ1L/GEkovyG78Gg=", "QryYyl8rkJhMdX4DtQMdRLlWgSOXDdHsh+GTntrnY9k=", "LuCzv6jJTWI1OwxV6lm7WuzCcpa7rHnpu3H8rFypYsI=", "k66PmpGv6jxxIXWo9HU7QwcYLndR0JbFT8YbjcrcbGE=", "Gf/j0Iot3r5fVIn2izwN+i5ZF4jWtQ0JuMIDQDh3OP8=", "q3mhx9oAjStG5oPVFZEJVsUCK3ShyXYEywFVez/zpzw=", "bH3Bpz9IL8Ex6z2nxEdK3tqsWYUGuMLVMoj5b5ZoaH8=", "ZeqPEiizFynKX94GRE72joaWqObRPFcS7pJycABRq8o=", "jrOkg6P8xEAqxslsh5/lEiNH07ENvZNBKIxrj7HjplA=", "t5nhAAxjGd972tYT6FcLy1rHlDHzPOkY1gJe4WbOiWU=", "XigNmSwZyZ9ak6kV4RWDOVSgAJgLdrD6BVYZ7Au3itM=", "SuMwtBncKxmBktrH+XvVp56sYg3rnqyKp2/4GQT/5pQ=", "AFozzZXo4boVC5y170WvfzYpm3O6WMtOMwAPuZ9/kmo=", "4aLowaSEwAsEOBfv1JW/r3P1xQxFCgLOtfczMO5r8pc=", "2Hl0YqT95To7pevFzYjUTa/i48h9lP+wENELb/DVHak=", "wUVSqPyH0Zvh1yfbYp5Re7GAmaZX7Z0VoSYNcJV/fsg=", "rz2ZCv+q3EnLo2pA3iVrVtpW0TjpRVNAGlVvBS3TAn0=", "of4/Gy+mdFMDr0WVJ2ug0LTh6/kYOVOhPcFxrRa+RUs=", "tB0s+KLvWmjK3KAhde6RPcpvJ7cKBRv+FQY/LS25Vd8=", "xQ+lvuiu5SZCG8Wk4nShpM8f6Tf0ZgI5KnAL6/sg9Es=", "IaTBTg5sADVgKlTlqsCn+xRElbdVzcgnVdjU7FHCspk=", "K9HpaMQ6V+Y92kduhIO6bjF62rA5itV0nQVhrz07bNI=", "mc5o7RPNqJBp3Rfkij1FKiRDoibU50r28aiLw8dPDH0=", "9KExVynmoJNbBYBq5ExXc4l7geEixss6Jba75CA4qRg=", "f1DgVG6bVMnqqB6BUNF1fLiUl/lqSlAL5NMrCTkzhgg=", "gMiQdyC8r23txzIF5MNjQKlioshzytUKqlB/uzFqJ18=", "kiY9WV/AhKX+DIUlo0T/Om1rHgclYEzSRTmQw4ORqS8=", "bNzR6vXP0aKvpLfK1GRb7kE45vXN6zBdsKnEsvWkLog=", "wx7NARmcbtkbe313fMN0LUP3S94CIvk4s6VH/jRREFQ=", "BGgg9lMy/T2ha2PacrfDu79wI8ZaOy5sSLWh69Cc4+s=", "cU12+3FHA5et4ZvaKrJC+BmnuD6h5Vh3Ayv4jhyWumo=", "k6zwufygeOcBzCV5QcfW/uw3E2DFL8Kk5iwZ3qJFk08=", "wJTxTT9IIxYfjc3nXm2ptOU/0tqdmwVGEUxGUxuV78s=", "IUArhVZVK+vSvfG6TPGCfTMDmY86GUb8R14uMc4G76E=", "Zwxhf/VyG5CTK5UCZHms6Gj4rH0+hXcJPte1Aw8Mwzs=", "1TqWEM74MOEutYW9R6Xh2t6i7VKOR525wEl5jOvpkAM=", "MIyIHSYBM7P8GJ8SUvB8M2K2nhYkW0z0R2A98algEn0=", "J4ADzc2Fe92K957X9zu6etoW2v+pTte/xHk8NlG0es0=", "z124AL08Mi9m2GhSa9lP29SqwNYTY//4PA7t7VvH0ds=", "iKwW5Mwjt/XVRDptLT8ycAqyAoRI4Wm3V8EsZHHUSo4=", "IF7diQQsvhGQIBPiMBwwAe0h4+XAp5p98sov56oviec=", "uH5ovuFt6QEoE/Iq+QLfVQiiitnXspWOecZQ4WTIr5I=", "z8r+AAV4iBH23bXB57vV35vIjK9MoQSwXBTceCklzbE=", "ioEY+HrAP2gCiSoJR9sXXzJbPr+RcJfhZNE/g6EzUTM=", "g2UjwdD/AUJg2a4tCPZMGxLqOdmmLUWr4+ArHfSI2S4=", "5DLje/lwD/sD2N7mv6jv+EPDN8tQChWhDJYVDLNSYSo=", "/R+25j4vi4degSE3EhkO//ymvCzbZQ9Fjlpr9t6HFEs=", "*******************************************=", "SVjAb9HGwYjMUmtKWFjSkC7I8jAusdJKLAK06CqFmpQ=", "StZBhZF1CA2zqdaF9WQYNsog+kxOmw/simR3h+zDTJE=", "/xo0ocHjTGEXyehYjh4GxUDUyZrvVp2srxwEKo8MyOA=", "GtJbRDxDP+Jky2qFTvMtQsNtPhK5+bVw6OuW8nM6C2Y=", "A6HvTCE5ZNgd7Q33ZPPS6Z7eDlBegntYacm6vD3v1D0=", "u+1plH4FdEciAf2voUhat1zEhHB/oZycrVsDihfzOVg=", "oXBdOGM2Cj2D4gCOB5Fo/+JVS+lkkBY3dMMGdTxmBD0=", "lCo4UHWuVynSmMLibZy7GsSqV+0fJ2cSbfEVLh/DXmc=", "CQa09JcAs6xl2b8RXugF52nq+BC4fbsmItO59afmgfA=", "hGzVpnMLqb1eC3Q5O13TnbqJGw735EGkurdHNXD3Lqw=", "uXLbb/GpiUkmW8mtvs3ZQJas9p6J8ThNX3r3nJ6FKcY=", "fcM8ojEmfsKpTpXW2IxSR6taYyQGw9X9q6DMp3A7YMU=", "nF2TNa8QOcT773+iIFo4aoHdSaVRJJOhI7dc/SekGRQ=", "nW59d4lJNM1G8ziKOrBZ/+STW0WtKz/VKtYMqs4jDQw="], "CachedAssets": {"GtJbRDxDP+Jky2qFTvMtQsNtPhK5+bVw6OuW8nM6C2Y=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\qsevoaibl6-x0q3zqp4vz.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tbedkqp182", "Integrity": "yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 670, "LastWriteTime": "2025-08-23T14:16:55.5556152+00:00"}, "/xo0ocHjTGEXyehYjh4GxUDUyZrvVp2srxwEKo8MyOA=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\w5xawwp0xo-ag7o75518u.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m7rg4chp1z", "Integrity": "Po4sWHCLIbaqYqyEYnIYtBO+dRjwxtbreif7YdpGQxM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8053, "LastWriteTime": "2025-08-23T14:16:55.5546152+00:00"}, "g2UjwdD/AUJg2a4tCPZMGxLqOdmmLUWr4+ArHfSI2S4=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\e4ilbmaqpi-47otxtyo56.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hj59c5yssu", "Integrity": "TWRCXMwzQLwwVw7TcG+7g24PsqDnarIGBgITeMiCp10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4663, "LastWriteTime": "2025-08-23T14:16:55.5470548+00:00"}, "ioEY+HrAP2gCiSoJR9sXXzJbPr+RcJfhZNE/g6EzUTM=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\mi2ywbwqzf-0j3bgjxly4.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s9vzkbm4vd", "Integrity": "EImWkSMXEOh40HFlhmq90VTXlOCYqWVzWn4vPxUhiA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55524, "LastWriteTime": "2025-08-23T14:16:55.5460577+00:00"}, "z8r+AAV4iBH23bXB57vV35vIjK9MoQSwXBTceCklzbE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\pd3osx1j2t-63fj8s7r0e.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnp63td8x7", "Integrity": "YQ6ZOC5+9dlRHEgMrLJKheWx2C2Xk7AL5Hy36EOfqq8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16571, "LastWriteTime": "2025-08-23T14:16:55.5955949+00:00"}, "uH5ovuFt6QEoE/Iq+QLfVQiiitnXspWOecZQ4WTIr5I=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\0iusvdy6j1-h1s4sie4z3.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fizolni560", "Integrity": "OeWySIbjCJXM4ZgVwiGmsjpLeFsT+qOpONsDqh8xk5M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64490, "LastWriteTime": "2025-08-23T14:16:55.5919695+00:00"}, "IF7diQQsvhGQIBPiMBwwAe0h4+XAp5p98sov56oviec=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\sd9m7ehn1p-notf2xhcfb.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "winy7sfnv8", "Integrity": "mLLuMjFYOIYX8xSc8UD/TLCKJOjlhcscgrxC5va1aAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29657, "LastWriteTime": "2025-08-23T14:16:55.5728466+00:00"}, "iKwW5Mwjt/XVRDptLT8ycAqyAoRI4Wm3V8EsZHHUSo4=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\fz05ukbwb1-y7v9cxd14o.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "03mhy7u5rs", "Integrity": "9Hc7N6ug4kO+ZAzS1pFxw7Hbp+FZaCKMnDLRAv1J/zw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56385, "LastWriteTime": "2025-08-23T14:16:55.5713449+00:00"}, "z124AL08Mi9m2GhSa9lP29SqwNYTY//4PA7t7VvH0ds=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\wrs469idxe-jj8uyg4cgr.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6t36crcw3z", "Integrity": "Ng/NTAPb2HvIa++U+TsQnZ6UYlXnzyFuh5Df7vHJZgU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18542, "LastWriteTime": "2025-08-23T14:16:55.5521152+00:00"}, "J4ADzc2Fe92K957X9zu6etoW2v+pTte/xHk8NlG0es0=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\c754nnd6t5-kbrnm935zg.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6e2hoibiq5", "Integrity": "oglZ6aWY/UXq+QsVhwhOwfIZbgvz7Dvjg/GfXmQsBVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64234, "LastWriteTime": "2025-08-23T14:16:55.5491227+00:00"}, "MIyIHSYBM7P8GJ8SUvB8M2K2nhYkW0z0R2A98algEn0=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\v1b5dn115g-vr1egmr9el.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9z27fl73l7", "Integrity": "K+JV1bWtCSH6pUbk4PUJUao1Gc0EagazN8O9VnhFaPE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28886, "LastWriteTime": "2025-08-23T14:16:55.5975901+00:00"}, "1TqWEM74MOEutYW9R6Xh2t6i7VKOR525wEl5jOvpkAM=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\4uf4gkhslb-iovd86k7lj.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4jjusqppj5", "Integrity": "YNK7tH4KLEhmt17m2X41sl74ZCDUilGpyhRBJKwG0P4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86518, "LastWriteTime": "2025-08-23T14:16:55.5889598+00:00"}, "Zwxhf/VyG5CTK5UCZHms6Gj4rH0+hXcJPte1Aw8Mwzs=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\nm00nhfuzv-493y06b0oq.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18qy0lzppu", "Integrity": "QZ0MSDfNrGl5pbF5ppDikD9YeYfOenEETYBSBemyReU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23799, "LastWriteTime": "2025-08-23T14:16:55.5713449+00:00"}, "IUArhVZVK+vSvfG6TPGCfTMDmY86GUb8R14uMc4G76E=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\avsz42ibum-6pdc2jztkx.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iwjfk8z8sl", "Integrity": "fgl+pethjpTJQrRxrFinuKNP9hP56Cpi71nXoWFk1oE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92277, "LastWriteTime": "2025-08-23T14:16:55.5929749+00:00"}, "wJTxTT9IIxYfjc3nXm2ptOU/0tqdmwVGEUxGUxuV78s=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\gket21i90m-6cfz1n2cew.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zqu1wssclk", "Integrity": "Fd462fjZQtrrvL/sHbIIZF7ktZgtHombUabahz+Nhoo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44461, "LastWriteTime": "2025-08-23T14:16:55.5701122+00:00"}, "k6zwufygeOcBzCV5QcfW/uw3E2DFL8Kk5iwZ3qJFk08=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\uthag3dln6-ft3s53vfgj.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uom9ela1zq", "Integrity": "4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91866, "LastWriteTime": "2025-08-23T14:16:55.5546152+00:00"}, "cU12+3FHA5et4ZvaKrJC+BmnuD6h5Vh3Ayv4jhyWumo=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\sm2fhm2nz9-pk9g2wxc8p.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3m4n8jcl3h", "Integrity": "5VQx+pcREAWRC7fSQ1OVsJtpTGF7SJ83RS8p2rovHHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 31137, "LastWriteTime": "2025-08-23T14:16:55.6282942+00:00"}, "BGgg9lMy/T2ha2PacrfDu79wI8ZaOy5sSLWh69Cc4+s=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\3aox6ijp0j-hrwsygsryq.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2knqut3gi", "Integrity": "YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114877, "LastWriteTime": "2025-08-23T14:16:55.6229417+00:00"}, "wx7NARmcbtkbe313fMN0LUP3S94CIvk4s6VH/jRREFQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\9l8iy4ydib-37tfw0ft22.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7kvveri0mt", "Integrity": "h+uVSG7EhchzF96LorZkFbGFBO8oQowufr5PhxsGq8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33366, "LastWriteTime": "2025-08-23T14:16:55.6006809+00:00"}, "bNzR6vXP0aKvpLfK1GRb7kE45vXN6zBdsKnEsvWkLog=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\ileshhmqnv-v0zj4ognzu.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77t5enldb7", "Integrity": "Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91960, "LastWriteTime": "2025-08-23T14:16:55.593986+00:00"}, "kiY9WV/AhKX+DIUlo0T/Om1rHgclYEzSRTmQw4ORqS8=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\agec645h1o-46ein0sx1k.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uhnhmx07wd", "Integrity": "zrRIXAC8ugCIlsRMgBBjTa8xli0BiiAqT375rZab79Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 31118, "LastWriteTime": "2025-08-23T14:16:55.5728466+00:00"}, "gMiQdyC8r23txzIF5MNjQKlioshzytUKqlB/uzFqJ18=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\jo9om37mc9-pj5nd1wqec.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glev5uv9kg", "Integrity": "pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 114902, "LastWriteTime": "2025-08-23T14:16:55.5701122+00:00"}, "f1DgVG6bVMnqqB6BUNF1fLiUl/lqSlAL5NMrCTkzhgg=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\bolmm54fzx-s35ty4nyc5.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dp6dicxiq4", "Integrity": "MfXRRxefGD0S0k5f3gXwZNQW7ELiStY01dZj+IbLlaw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33478, "LastWriteTime": "2025-08-23T14:16:55.577875+00:00"}, "9KExVynmoJNbBYBq5ExXc4l7geEixss6Jba75CA4qRg=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\ws5y4i2ncz-nvvlpmu67g.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gru265j2n", "Integrity": "cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24456, "LastWriteTime": "2025-08-23T14:16:55.5651227+00:00"}, "mc5o7RPNqJBp3Rfkij1FKiRDoibU50r28aiLw8dPDH0=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\bcfvc6eeuo-06098lyss8.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ukjvmro89", "Integrity": "QDcr93RhSeRIcvSG2UO4XpXn7SmN9PibCcICeplm1tk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11127, "LastWriteTime": "2025-08-23T14:16:55.5521152+00:00"}, "K9HpaMQ6V+Y92kduhIO6bjF62rA5itV0nQVhrz07bNI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\hzdb0zu3u1-j5mq2jizvt.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pnmw3mh9ht", "Integrity": "tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44237, "LastWriteTime": "2025-08-23T14:16:55.5661219+00:00"}, "IaTBTg5sADVgKlTlqsCn+xRElbdVzcgnVdjU7FHCspk=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\htfovdwilw-tdbxkamptv.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "letd63iq5t", "Integrity": "49exWGtFTeHrxPgcV2SALyeXqbJ0Gx2BAW7ghwTbDHs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12069, "LastWriteTime": "2025-08-23T14:16:55.5460577+00:00"}, "xQ+lvuiu5SZCG8Wk4nShpM8f6Tf0ZgI5KnAL6/sg9Es=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\z7xgnmvvx1-c2oey78nd0.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oiolxl3gck", "Integrity": "oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24487, "LastWriteTime": "2025-08-23T14:16:55.5403723+00:00"}, "tB0s+KLvWmjK3KAhde6RPcpvJ7cKBRv+FQY/LS25Vd8=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\2uu6780516-lcd1t2u6c8.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cy4j794sg8", "Integrity": "LPVzscuhkQC3vZzaHKyyJLBxtZ0JDszgPmlT3zj1wGg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11149, "LastWriteTime": "2025-08-23T14:16:55.5691141+00:00"}, "of4/Gy+mdFMDr0WVJ2ug0LTh6/kYOVOhPcFxrRa+RUs=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\gg071mlfn2-r4e9w2rdcm.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21g01g2f66", "Integrity": "JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44266, "LastWriteTime": "2025-08-23T14:16:55.5671195+00:00"}, "rz2ZCv+q3EnLo2pA3iVrVtpW0TjpRVNAGlVvBS3TAn0=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\87doal687n-khv3u5hwcm.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3lpifp44y", "Integrity": "9EqjQR8ugEerwGk0t0elN3RQ+XHT25kPo1+/yYXwvNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12127, "LastWriteTime": "2025-08-23T14:16:55.553112+00:00"}, "wUVSqPyH0Zvh1yfbYp5Re7GAmaZX7Z0VoSYNcJV/fsg=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\mckc902dqn-jd9uben2k1.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ldvkm706vq", "Integrity": "J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15028, "LastWriteTime": "2025-08-23T14:16:55.5491227+00:00"}, "2Hl0YqT95To7pevFzYjUTa/i48h9lP+wENELb/DVHak=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\ottd6rjozf-dxx9fxp4il.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b8ltiz8u9h", "Integrity": "u85nx859JKxhSe8TTSe97CuakJmuU36EC4JdxkCZL3g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3231, "LastWriteTime": "2025-08-23T14:16:55.5403723+00:00"}, "4aLowaSEwAsEOBfv1JW/r3P1xQxFCgLOtfczMO5r8pc=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\qbmw8bvje5-ee0r1s7dh0.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqk14ew2nl", "Integrity": "PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25726, "LastWriteTime": "2025-08-23T14:16:55.5393751+00:00"}, "AFozzZXo4boVC5y170WvfzYpm3O6WMtOMwAPuZ9/kmo=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\sft3se1fdb-rzd6atqjts.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vkbr9nomgm", "Integrity": "oSfmOJQjIULAN8mPr84YlRct/JVoFbWAn9Y7IWzBuoc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3364, "LastWriteTime": "2025-08-23T14:16:55.5521152+00:00"}, "SuMwtBncKxmBktrH+XvVp56sYg3rnqyKp2/4GQT/5pQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\43nmtc4zoi-fsbi9cje9m.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ggfb0v5ylw", "Integrity": "ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12568, "LastWriteTime": "2025-08-23T14:16:55.5511173+00:00"}, "XigNmSwZyZ9ak6kV4RWDOVSgAJgLdrD6BVYZ7Au3itM=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\1cqk3p69bj-b7pk76d08c.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5fx04t62wt", "Integrity": "z14pI3bmpzGTIVaJu6aKOcry1zSAu/H44LHt9J3bXFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3199, "LastWriteTime": "2025-08-23T14:16:55.5460577+00:00"}, "t5nhAAxjGd972tYT6FcLy1rHlDHzPOkY1gJe4WbOiWU=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\am5o9nnr85-fvhpjtyr6v.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b7ig2cj79", "Integrity": "fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25714, "LastWriteTime": "2025-08-23T14:16:55.5448853+00:00"}, "jrOkg6P8xEAqxslsh5/lEiNH07ENvZNBKIxrj7HjplA=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\098pwrx65d-ub07r2b239.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "syqesifl59", "Integrity": "/Cti7KtgK7n0FoWSWRMvmhTwGSxieEUp+Ao+6Xxnmug=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3375, "LastWriteTime": "2025-08-23T14:16:55.5383754+00:00"}, "ZeqPEiizFynKX94GRE72joaWqObRPFcS7pJycABRq8o=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\mjqoea1691-cosvhxvwiu.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f178oapzb7", "Integrity": "XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 14093, "LastWriteTime": "2025-08-23T14:16:55.5378759+00:00"}, "bH3Bpz9IL8Ex6z2nxEdK3tqsWYUGuMLVMoj5b5ZoaH8=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\pg81squhx7-k8d9w2qqmf.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ykjynei6kc", "Integrity": "Xll87KRp2km7kw+kDSFiBiyUQnZ7QCVIb8yJtRDXckQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 6104, "LastWriteTime": "2025-08-23T14:16:55.5671195+00:00"}, "q3mhx9oAjStG5oPVFZEJVsUCK3ShyXYEywFVez/zpzw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\2ijew7yb6j-ausgxo2sd3.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnst782kog", "Integrity": "ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 33061, "LastWriteTime": "2025-08-23T14:16:55.5641253+00:00"}, "Gf/j0Iot3r5fVIn2izwN+i5ZF4jWtQ0JuMIDQDh3OP8=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\jsy8za076j-d7shbmvgxk.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "316ql842l5", "Integrity": "+UkMsfq0zPH03cQtfw9papLT2sHleN3bw7S1s5iLonY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6918, "LastWriteTime": "2025-08-23T14:16:55.5491227+00:00"}, "k66PmpGv6jxxIXWo9HU7QwcYLndR0JbFT8YbjcrcbGE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\918sh73gz3-aexeepp0ev.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3latwtrz94", "Integrity": "1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 14072, "LastWriteTime": "2025-08-23T14:16:55.5470548+00:00"}, "LuCzv6jJTWI1OwxV6lm7WuzCcpa7rHnpu3H8rFypYsI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\9ifpczta08-erw9l3u2r3.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s32t06avo3", "Integrity": "6OfodiC592J173EssQMUTEfylPvm7++5l9PRY+7T5Uo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6102, "LastWriteTime": "2025-08-23T14:16:55.5438882+00:00"}, "QryYyl8rkJhMdX4DtQMdRLlWgSOXDdHsh+GTntrnY9k=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\nqzy5ejve6-c2jlpeoesf.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bpa5g0wlhg", "Integrity": "keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 33080, "LastWriteTime": "2025-08-23T14:16:55.5413698+00:00"}, "DjYUoLHxTVeULejwjHJexhpXuIYoQ1L/GEkovyG78Gg=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\w08146su8i-bqjiyaj88i.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p1uwaguxw6", "Integrity": "HQp0c6N8q5wOxo27/7+5nSrp8SBfCepJdWIMB3pb5bE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6915, "LastWriteTime": "2025-08-23T14:16:55.5470548+00:00"}, "d5lRVdLv1YwwCgL+y0HGcmitnkyBTJ5B9VPL1g8H22Q=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\kkibihrusd-xtxxf3hu2r.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fpxp8wntf7", "Integrity": "465WqepNnI4ENLgQJFXrL2gtI20GCfeLK3JAtugOzpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\site.js", "FileLength": 190, "LastWriteTime": "2025-08-23T14:16:55.5448853+00:00"}, "TW4Ig4EbhaYR0P/B4Ayvoqoj3Y6qd041iI7NQySpCyE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\yjd09cy5he-cszcmsjyt3.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/Notification#[.{fingerprint=cszcmsjyt3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Notification.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tu5qv04art", "Integrity": "Jd/21Oq4uqdV2m5JNpwyWeg8L1MIvW2HoeBT2Ul62W0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Notification.js", "FileLength": 1207, "LastWriteTime": "2025-08-23T14:16:55.5438882+00:00"}, "/Gr8Io7HICETrWjhZHQ95bn2W2V/pDDmPKFqcNgSaRQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\07o74jw1x0-3xv8oehij3.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/adminlte.min.js#[.{fingerprint=3xv8oehij3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2gkl8c43zc", "Integrity": "whLmWz82zSBFERXMGk/VTRQ5+SE4TUwol2irIBrfsNk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.min.js.map", "FileLength": 7971, "LastWriteTime": "2025-08-23T14:16:55.5438882+00:00"}, "w1V8ZbOLh2G5zNrPGbz5VexwCZ9npgQ1eEiJoXYDpyw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\3dm4jso9l0-wmxgv7s433.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/adminlte.min#[.{fingerprint=wmxgv7s433}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3cpjbuw7w5", "Integrity": "A+owLV5kmDdbVZeyXDXTG3MZvyfJr3GCSETIJZyiTHU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.min.js", "FileLength": 2871, "LastWriteTime": "2025-08-23T14:16:55.5403723+00:00"}, "99y6es7jHBhlJvwUHzTRpWoooZ//4vl+YGEFff5q3ds=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\57gidv2bgb-ddky5r22iq.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/adminlte.js#[.{fingerprint=ddky5r22iq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hl24sqcdla", "Integrity": "JSrg203XuUF7d48k9WTykCOOGtsCks5y6MQdiQKu7jQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.js.map", "FileLength": 7698, "LastWriteTime": "2025-08-23T14:16:55.5328619+00:00"}, "7RFmxeUxFgVN5X/i4amO0bPWFcGzakeYX7vZ5ht2kDQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\19xlqrmlmt-qacilm03ac.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/adminlte#[.{fingerprint=qacilm03ac}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r93n1k4t0u", "Integrity": "sZ7fcijmmai+mdARJkKFa1XHnyPHy4nJzz+cb0w7NXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\adminlte.js", "FileLength": 4536, "LastWriteTime": "2025-08-23T14:16:55.5701122+00:00"}, "Z9bCZ3NQbQFDhXlH39nB2/XAMvUxyAJUYxwiPKQRrNc=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\mluc9wk1qu-61n19gt1b8.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lir1p715ud", "Integrity": "jcY0RXFOlcp4VNTCcYQtWVZZBlr+ZY+AOv6/0S5dnF8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\favicon.ico", "FileLength": 2432, "LastWriteTime": "2025-08-23T14:16:55.5691141+00:00"}, "h9sOYCf4WIZAEV6e1Yv6Bd+Z0/Ydlt+H7BhO1mXVX84=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\v2iwyzc81s-b9sayid5wm.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/site#[.{fingerprint=b9sayid5wm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wf4wcyqcqt", "Integrity": "fNKkLSNFLZMVTc2zVrpMXQqOwPU3sU4GWcfEbCR2V1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\site.css", "FileLength": 319, "LastWriteTime": "2025-08-23T14:16:55.5681169+00:00"}, "xwc1TyIltwaQpHf4S6VwugfJ7saF8a/6lOuozkYuOVY=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\7ueipg6yon-qm74najt5r.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/dashbord/dashbord#[.{fingerprint=qm74najt5r}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\dashbord\\dashbord.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jdjorxy6rj", "Integrity": "FzfhW2H3mhEWJD9NtAQg1F3viEwm68L7uVMdbyXFn28=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\dashbord\\dashbord.css", "FileLength": 1632, "LastWriteTime": "2025-08-23T14:16:55.5828813+00:00"}, "b3Nn1ZOWtt5z1jS7EbEiTlPvDkCVIwZ+1txggoXSRDg=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\4fo57njcvc-q8ls3kvgdx.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.rtl.min.css#[.{fingerprint=q8ls3kvgdx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "msdppgumrg", "Integrity": "6pQqd3+r5L/timysBH8uiuVtsOgbAkAXwdX5J/hp+Rg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.min.css.map", "FileLength": 159007, "LastWriteTime": "2025-08-23T14:16:55.5828813+00:00"}, "9zVasDxcwgDw2/5VMH2YTjpln7AIkGrzMNRr9aADp8o=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\hv2s4sy3kv-g954zfyzc2.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.rtl.min#[.{fingerprint=g954zfyzc2}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7489gvabn6", "Integrity": "qSJDEoDRTTfUvRjIEY5hLIKKQG1HPtlQtWvlhnGsL9s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.min.css", "FileLength": 40199, "LastWriteTime": "2025-08-23T14:16:55.5428768+00:00"}, "XOID14bq2l0wziWGJgQTq6hKs3+uedC3XFuaFEbRS3Y=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\29ihgeo6w0-a1v63x9wfv.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.rtl.css#[.{fingerprint=a1v63x9wfv}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2afzuyftwg", "Integrity": "Gg5T3RaCb5MH7gp6rJgFM1qH+BEtjaBAz2dXGA0wOn8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.css.map", "FileLength": 142868, "LastWriteTime": "2025-08-23T14:16:55.732079+00:00"}, "/m9admfEScF0Gc/NDWKiZHJlJhGZJsBJxzREnHHsG/M=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\9d8fkingo8-nf8rx9prei.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.rtl#[.{fingerprint=nf8rx9prei}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ajhi4mdcvz", "Integrity": "umrYHYoyrmyFKuWRBlaw2iNxyz4dKfpI5EoKhVblFfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.rtl.css", "FileLength": 43726, "LastWriteTime": "2025-08-23T14:16:55.7129051+00:00"}, "kn8nvFPV03qEdwExyjM9YBWLvvZZ/4FFqOCnaSQFs98=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\jtxtv1g4wz-9ccuhph3ns.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.min.css#[.{fingerprint=9ccuhph3ns}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ehlg0x4de9", "Integrity": "Poutzrf2mGKTlf5meOgdgSO0qW9/v6HJwON8GCRHlHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.min.css.map", "FileLength": 391646, "LastWriteTime": "2025-08-23T14:16:55.7064186+00:00"}, "Om/EFo0w+A3YWA15EYEiS/vCUNaWQHGSS5SiOYlNIvk=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\j9p6ch9y7u-13ti5y8wq9.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.min#[.{fingerprint=13ti5y8wq9}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x92yb3026z", "Integrity": "BZfgYY2vvRMcszRLoTgFTJi5SXApSFErU7jqlYHbGBY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.min.css", "FileLength": 123185, "LastWriteTime": "2025-08-23T14:16:55.6452355+00:00"}, "B/YzT5lWdgrwle8SmC9TaZNwNrcyzuCgwlg7RLeYcxU=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\8jogvlxm5l-la7x5y3yhh.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte.css#[.{fingerprint=la7x5y3yhh}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ek8sgw0z06", "Integrity": "opWy9NLSDke0cB6drwyi3QKdAfa+XpW6M4uRhBqZAW4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.css.map", "FileLength": 324673, "LastWriteTime": "2025-08-23T14:16:55.6261607+00:00"}, "sjKhI4h2MuDUKrYpwE9pQ52R96IEZWY5qyMBDJCxztE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\ikrel3tmcy-b3s2zvn7in.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/adminlte#[.{fingerprint=b3s2zvn7in}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f6twztrkdl", "Integrity": "t6KRfsLhZPENZkhBcJ42Qs2bLKrvES5OESoxDJAUqS0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\adminlte.css", "FileLength": 130322, "LastWriteTime": "2025-08-23T17:28:22.7701938+00:00"}, "A6HvTCE5ZNgd7Q33ZPPS6Z7eDlBegntYacm6vD3v1D0=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\0bnvwq0n2y-0i3buxo5is.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v75tr20pas", "Integrity": "q0KM+9yOLRYeTH/iGBE/HUYO08aXn2DmZ+yyIU6/kho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84055, "LastWriteTime": "2025-08-23T14:16:55.5728466+00:00"}, "nW59d4lJNM1G8ziKOrBZ/+STW0WtKz/VKtYMqs4jDQw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\8390f5fr05-o9kdthts58.gz", "SourceId": "PL", "SourceType": "Computed", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "PL#[.{fingerprint=o9kdthts58}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\PL.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "70y1rwl0f1", "Integrity": "Zy2ZMqxgrk8jVm0dnfi1AoNpvOrZMu1i/WGfUQ1V5Qw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\PL.bundle.scp.css", "FileLength": 535, "LastWriteTime": "2025-08-23T14:16:55.5566126+00:00"}, "nF2TNa8QOcT773+iIFo4aoHdSaVRJJOhI7dc/SekGRQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\lvxowre8w3-o9kdthts58.gz", "SourceId": "PL", "SourceType": "Computed", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "PL#[.{fingerprint=o9kdthts58}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\scopedcss\\bundle\\PL.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "70y1rwl0f1", "Integrity": "Zy2ZMqxgrk8jVm0dnfi1AoNpvOrZMu1i/WGfUQ1V5Qw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\scopedcss\\bundle\\PL.styles.css", "FileLength": 535, "LastWriteTime": "2025-08-23T14:16:55.5566126+00:00"}, "uXLbb/GpiUkmW8mtvs3ZQJas9p6J8ThNX3r3nJ6FKcY=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\1frxqwvvbx-mlv21k5csn.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rdl0higpfn", "Integrity": "wRqs30CSrwWDvG1bDMUjomAlSqRRTMplLSuLcrgYtHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 669, "LastWriteTime": "2025-08-23T14:16:55.5556152+00:00"}, "hGzVpnMLqb1eC3Q5O13TnbqJGw735EGkurdHNXD3Lqw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\rcxh2ukh6d-87fc7y1x7t.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uiwq7himce", "Integrity": "2u2XFgZyC96dP/elrO5ny/maD/llVl8JsX3pBm2SWT0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 42735, "LastWriteTime": "2025-08-23T14:16:55.5681169+00:00"}, "CQa09JcAs6xl2b8RXugF52nq+BC4fbsmItO59afmgfA=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\1ba3i9i8ap-muycvpuwrr.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gm2ug2dj20", "Integrity": "OK9mKWiOoFmOZ+xYop9MRSKE6LHMu/9ZTCPvluUOpcA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24089, "LastWriteTime": "2025-08-23T14:16:55.5511173+00:00"}, "lCo4UHWuVynSmMLibZy7GsSqV+0fJ2cSbfEVLh/DXmc=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\6u32wklwip-2z0ns9nrw6.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yow8h3bl0n", "Integrity": "fhspAbITSYOUuRd6s5oYdr7V1DC3ibPbW5Siv7MGps8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68315, "LastWriteTime": "2025-08-23T14:16:55.5470548+00:00"}, "oXBdOGM2Cj2D4gCOB5Fo/+JVS+lkkBY3dMMGdTxmBD0=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\7jdcsiblwy-ttgo8qnofa.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sdwt97mj8r", "Integrity": "0HH7P7Mtrs+2sBMSFXD8DJw0oV4iDB5wkeRFpSi113A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 53929, "LastWriteTime": "2025-08-23T14:16:55.593986+00:00"}, "u+1plH4FdEciAf2voUhat1zEhHB/oZycrVsDihfzOVg=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\8w1oxxn4zp-o1o13a6vjx.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9lbv9l0z68", "Integrity": "bSlV8nIWgnEsf0ljiCRK0xw364UXDcqZZilq1CQ+At0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30413, "LastWriteTime": "2025-08-23T14:16:55.577875+00:00"}, "StZBhZF1CA2zqdaF9WQYNsog+kxOmw/simR3h+zDTJE=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\iobsf6gqf6-lzl9nlhx6b.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w44s95dr06", "Integrity": "8HCMA1neUf8Vrpg7qfQgzCVX6n/hFKgcFl5RKQuVC1k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14023, "LastWriteTime": "2025-08-23T14:16:55.5328619+00:00"}, "SVjAb9HGwYjMUmtKWFjSkC7I8jAusdJKLAK06CqFmpQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\4970a329yt-mrlpezrjn3.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ka6j29rtmm", "Integrity": "nJwi+an4Wr4A7wVYax2DH+rKvwNbT+YQBBWTeP8GLr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6382, "LastWriteTime": "2025-08-23T14:16:55.5546152+00:00"}, "*******************************************=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\zyd0cmgl4r-83jwlth58m.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pvtge0zj2y", "Integrity": "u0Z1XIrPIXG0QZK+EQOG85U9O7JhC+GVwFW+Rcyvbyw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 13955, "LastWriteTime": "2025-08-23T14:16:55.553112+00:00"}, "/R+25j4vi4degSE3EhkO//ymvCzbZQ9Fjlpr9t6HFEs=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\cxum7dsckr-356vix0kms.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0vtotz30el", "Integrity": "1HkjQSVuV0/ji/X/lGeDTHeagBS0BwopMrHDQuwqgtU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 680, "LastWriteTime": "2025-08-23T14:16:55.5491227+00:00"}, "5DLje/lwD/sD2N7mv6jv+EPDN8tQChWhDJYVDLNSYSo=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\xrokdvjgvu-4v8eqarkd7.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tabrq1ho0f", "Integrity": "9X+6mMpWl0sRImYav09zbU/QCCt+GeH7cDib6C/z2lI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2202, "LastWriteTime": "2025-08-23T14:16:55.5481256+00:00"}, "6byYB8JYZg11sD2DkjxZkPa1g8CjtWwAV56uJz6d4iQ=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\azrc1f45kk-45cuv64g1r.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/ForgotPassword#[.{fingerprint=45cuv64g1r}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPassword.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnvhxxft7f", "Integrity": "fzB05OOP58CoTMQxWLJ3HVw2KDevkKmbn7p/q+D/ozE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPassword.css", "FileLength": 572, "LastWriteTime": "2025-08-23T14:27:59.81489+00:00"}, "jb2SuEUv9iv2MagZcdwlFd3inDXl6E3CRJFfj3yptPI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\33igy13wa8-9vzcw40cgq.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/ForgotPasswordConfirmation#[.{fingerprint=9vzcw40cgq}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kzud3shful", "Integrity": "4H738bhWNmr2xY7fVMwQ3MDC5ly41lNs5tX9a0mSdtU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ForgotPasswordConfirmation.css", "FileLength": 517, "LastWriteTime": "2025-08-23T14:50:15.9136032+00:00"}, "+Pvve9wtdrW+U9VeMu/4QZvB9M05BU7MQ4jzKWr81iw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\bsjxzejyqv-9oqg7hy70n.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/Login#[.{fingerprint=9oqg7hy70n}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Login.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xpopyov234", "Integrity": "m4S0aCCU0YrcWn+l0KKPAqgyH0N6nedJdIzmOQfi3Lk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Login.css", "FileLength": 967, "LastWriteTime": "2025-08-23T14:50:15.9136032+00:00"}, "oBj+9+KJhQzndPtc9vaI3Ji8MtOM1AbOdLj8miNw0Xw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\ciqt2dnwz8-vk0m4z9v8p.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/Auth/Login#[.{fingerprint=vk0m4z9v8p}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Login.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rus4qyny4v", "Integrity": "7YLZxJ3Sb4vpU8nO0n92KSyXd6NS1aSkUZD3DZIwGrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Login.js", "FileLength": 351, "LastWriteTime": "2025-08-23T14:50:15.9136032+00:00"}, "is1Axy/JmkDx+9MRKVr4NxXQE4shKlQkxkqDCvnSKaw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\eco4hs712t-fmnrw6fry6.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/home-dashboard#[.{fingerprint=fmnrw6fry6}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\home-dashboard.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "70wov7uhez", "Integrity": "+Sy5uY4olPLfOgENq3raUQEYXxugfWSL8Qm0V2naeLI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\home-dashboard.css", "FileLength": 2131, "LastWriteTime": "2025-08-23T16:58:55.4708672+00:00"}, "ck+8WOcWzV76/BI2ZaFce75Wl1UqarGzr/jzskC8WHw=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\f10mz3ynff-ialj5o0ldp.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/user-edit-profile#[.{fingerprint=ialj5o0ldp}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\user-edit-profile.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dhnk3hhnag", "Integrity": "4Upykd3Jh6Bel4zz1vZWKlBZux2oMQjA8WXGugqkRxY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\user-edit-profile.css", "FileLength": 364, "LastWriteTime": "2025-08-23T16:58:55.4708672+00:00"}, "q5RUBI+teJ4SPhxLmVwpK9ffwC6K1FUwwgW0sNZX/34=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\p8wobp2dkb-ifja4tpxez.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/user-index#[.{fingerprint=ifja4tpxez}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\user-index.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1gms0ie08i", "Integrity": "XzcpcU5Osff91715m6oAV7lB/Zukl8oVb7RNSi3yzgg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\user-index.css", "FileLength": 216, "LastWriteTime": "2025-08-23T16:58:55.4708672+00:00"}, "Rr4FE4zG7AYs3AB5/zZ2qwaCgiHgpOYaU7SEL/Q+ZTI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\7a9ascve2q-bhzg9jivkj.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/user-profile#[.{fingerprint=bhzg9jivkj}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\user-profile.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c8d7d9ac8b", "Integrity": "RDDYbDzuZGqe9Xhn5UuTx/ImMLPJaMTgbZkmmgo1osA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\user-profile.css", "FileLength": 209, "LastWriteTime": "2025-08-23T16:58:55.4708672+00:00"}, "3srLWQf8+mTb67fXtgmDUjlPxQKjTfb9148YhMLpAVg=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\t3chh267ty-mw2y50nlgq.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/home-dashboard#[.{fingerprint=mw2y50nlgq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\home-dashboard.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wtb09l1io7", "Integrity": "HBdmoyDjtXLFjNNXUEnI90Afz9PNUbazzzvuIAr/X8A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\home-dashboard.js", "FileLength": 1726, "LastWriteTime": "2025-08-23T16:58:55.4708672+00:00"}, "yMLciXalY7+qz3T8O49LA1qOtrQ0yuexLgrxveNLW0k=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\uuzpqqrmn3-uxltzax0eb.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/layout#[.{fingerprint=uxltzax0eb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\layout.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o1791dwry9", "Integrity": "FL+8XtAKi2S2TAYrJwq2n5zhqhKsJODRzK3pCb11vdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\layout.js", "FileLength": 1055, "LastWriteTime": "2025-08-23T16:58:55.4708672+00:00"}, "lDDKlX/D+i+gzvUInXzIyN6fKPgq019rnG1TvH7tuTA=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\tyrkpe7989-8semrowlze.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/user-edit-profile#[.{fingerprint=8semrowlze}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\user-edit-profile.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m3s2nt41hz", "Integrity": "woVbo/Ybj2UTIaPfrlKRxHJWcDHANuyVbaaW+ttPj00=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\user-edit-profile.js", "FileLength": 233, "LastWriteTime": "2025-08-23T16:58:55.4708672+00:00"}, "/PZ4GiEh34bWS1VOgQLO6M6X3m5rpOthSogqYuCe8o4=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\mgpr37k73r-fmtqv1f0zs.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/user-index#[.{fingerprint=fmtqv1f0zs}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\user-index.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jpcvgavn57", "Integrity": "6YVZ0NQf+JrbdU9GK5IzgqKqIdO+p1kJvpi8JJmAlqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\user-index.js", "FileLength": 412, "LastWriteTime": "2025-08-23T16:58:55.4708672+00:00"}, "V9HQqqV5HXpvD7SnYVf4lcUB6qCNx3N0Mu2GakOMfLo=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\yfc05qif5k-08i07q0sn6.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/user-profile#[.{fingerprint=08i07q0sn6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\user-profile.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ntl7a18xor", "Integrity": "xed8Y13QMW22bluVc1tHSMzyPPEolHs3MtbfYP8xAOY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\user-profile.js", "FileLength": 615, "LastWriteTime": "2025-08-23T16:58:55.4708672+00:00"}, "DQrJhOaia4hOBm5GJ4R9AfGl4Br2cYlHx0D8n7uGfzg=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\vfgdcjydeo-j68zdcrzti.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/Rigester#[.{fingerprint=j68zd<PERSON><PERSON><PERSON>}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Rigester.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jy11zyqagh", "Integrity": "K6tVWQiWvXwkkxsk9FAksH3ONfNgQNRf3YADDrAVnPY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Rigester.css", "FileLength": 726, "LastWriteTime": "2025-08-23T18:30:22.4957584+00:00"}, "cDlVbJ6RJC3CB4CtlzZ49gZrmLSk07nFX7jVbc4p8jI=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\r3oy1ly4nl-wms42xh9an.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/Auth/Rigester#[.{fingerprint=wms42xh9an}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Rigester.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcis4ixu2q", "Integrity": "FRhf1+8QbRSo79Q/cvPDLYC9b6MJ7L11+NHA8xQTwQY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\Rigester.js", "FileLength": 380, "LastWriteTime": "2025-08-23T18:32:00.8404309+00:00"}, "nMhRn2OKOQFBQ+u7JJCc/9tBWaTuW5Q+MW+i3ucUVZA=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\xps233hi0q-537ycjson4.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/Layout#[.{fingerprint=537ycjson4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Layout.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jt73h13uys", "Integrity": "V09r/wQOUJMyXfLmSzwjT0pCpHpyl+MGrR8wjBGBs34=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\Layout.css", "FileLength": 290, "LastWriteTime": "2025-08-23T18:26:54.3900181+00:00"}, "KBXNBticn2Ib83XYHm0Yr6h3GUjaT3A1rXJw7T0YLYY=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\7nzdd3gm9b-e44xbp7gr0.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/ResetPassword#[.{fingerprint=e44xbp7gr0}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPassword.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8rz5<PERSON>wytz4", "Integrity": "RGEQ/w4HyZtK5ld5PrmKD/E86AvTmOTyz635Z/Cx8Yk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPassword.css", "FileLength": 709, "LastWriteTime": "2025-08-23T18:38:10.5809397+00:00"}, "7o0ZuZ1cSB70eZX/804W7QBmdojcpODysgY3UFjIoWM=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\opupkvti2y-pi47rgj3mu.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "js/Auth/ResetPassword#[.{fingerprint=pi47rgj3mu}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\ResetPassword.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dpvg26wpl3", "Integrity": "gV7WXAmUxGohiya25w+W2JbXrfC2OGcDU12KtmFNuzs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\js\\Auth\\ResetPassword.js", "FileLength": 658, "LastWriteTime": "2025-08-23T18:38:10.5809397+00:00"}, "fcM8ojEmfsKpTpXW2IxSR6taYyQGw9X9q6DMp3A7YMU=": {"Identity": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\2kexli4s27-9mfsvlb7fx.gz", "SourceId": "PL", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/PL", "RelativePath": "css/Auth/ResetPasswordConfirmation#[.{fingerprint=9mfsvlb7fx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eltkz5k96j", "Integrity": "0om0ueppxfEWSkmGxCytl103xwf2etCNaeWW57MukCU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\NET\\Projects\\templatewithView\\Template-master\\PL\\wwwroot\\css\\Auth\\ResetPasswordConfirmation.css", "FileLength": 750, "LastWriteTime": "2025-08-23T18:38:10.5809397+00:00"}}, "CachedCopyCandidates": {}}