@model IEnumerable<User>
@{
    // ViewData["Title"] = "مستخدمو القسم";
    // Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-4" dir="rtl">
    <div class="card shadow-lg border-0 rounded-4">
        <div class="card-header bg-primary text-white p-4">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0"><i class="fas fa-users ml-2"></i> إدارة المستخدمين</h2>
                <a asp-action="Create" class="btn btn-light">
                    <i class="fas fa-user-plus ml-2"></i> إضافة مستخدم جديد
                </a>
                <button class="btn btn-light rounded-pill" data-bs-toggle="modal" data-bs-target="#importUsersModal">
                    <i class="fas fa-file-import"></i> استيراد مستخدمين
                </button>
            </div>
        </div>

        <div class="card-body p-4">
            <!-- Search and Filter Card -->
            <div class="card mb-4 shadow-sm border-0 rounded-3">
                <div class="card-header bg-light py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-filter ml-2"></i> البحث والتصفية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label fw-bold">الدور:</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="fas fa-user-tag text-primary"></i>
                                </span>
                                <select name="roleName" class="form-select" asp-items="ViewBag.Roles" onchange="this.form.submit()">
                                    <option value="">-- جميع الأدوار --</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label fw-bold">بحث:</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="fas fa-search text-primary"></i>
                                </span>
                                <input type="text" name="searchTerm" class="form-control"
                                       placeholder="ابحث باستخدام الاسم أو البريد الإلكتروني أو رقم الهوية..."
                                       value="@ViewBag.SearchTerm">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100 py-2">
                                <i class="fas fa-search ml-2"></i> بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>

                <div class="alert alert-info d-flex align-items-center">
                    <i class="fas fa-user-tag fa-lg ml-3"></i>
                    <div>
                        <h5 class="mb-1">الدور: @ViewBag.SelectedRoleName</h5>
                        <p class="mb-0">عرض @Model.Count() مستخدم بهذا الدور</p>
                    </div>
                </div>

            <!-- Users Table -->
            <div class="table-responsive">
                <table id="departmentUsersTable" class="table table-hover align-middle border">
                    <thead class="table-dark text-center">
                        <tr>
                            <th class="border-start-0">الاسم الكامل</th>
                            <th>البريد الإلكتروني</th>
                           
                            <th>الأدوار</th>
                          
                            <th class="border-end-0">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody style="text-align:center" >
                        @if (Model.Any())
                        {
                            foreach (var user in Model)
                            {
                                @Html.Partial("_DepartmentUserRow", user)
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="7" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="fas fa-users-slash fa-4x text-muted mb-3"></i>
                                        <h5 class="text-muted">لم يتم العثور على مستخدمين</h5>
                                        <p class="text-muted">حاول تغيير معايير البحث أو إضافة مستخدمين جدد</p>
                                        <a asp-action="Create" class="btn btn-primary mt-2">
                                            <i class="fas fa-user-plus ml-2"></i> إضافة مستخدم جديد
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination Controls -->
            @if (ViewBag.TotalPages > 1)
            {
                <div class="mt-4">
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            @{
                                int totalPages = ViewBag.TotalPages ?? 1;
                                int currentPage = ViewBag.CurrentPage ?? 1;
                                int maxPagesToShow = 5;
                                int startPage = Math.Max(1, currentPage - (maxPagesToShow / 2));
                                int endPage = Math.Min(totalPages, startPage + maxPagesToShow - 1);

                                // Adjust startPage if we're near the end
                                if (endPage - startPage + 1 < maxPagesToShow)
                                {
                                    startPage = Math.Max(1, endPage - maxPagesToShow + 1);
                                }
                            }

                            <!-- Previous button -->
                            <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                <a class="page-link" href="@Url.Action("DepartmentUsers", new { department = ViewBag.SelectedDepartmentName, searchTerm = ViewBag.SearchTerm, page = currentPage - 1 })" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>

                            <!-- Page numbers -->
                            @for (int i = startPage; i <= endPage; i++)
                            {
                                <li class="page-item @(i == currentPage ? "active" : "")">
                                    <a class="page-link" href="@Url.Action("DepartmentUsers", new { department = ViewBag.SelectedDepartmentName, roleName = ViewBag.SelectedRoleName, searchTerm = ViewBag.SearchTerm, page = i })">@i</a>
                                </li>
                            }

                            <!-- Next button -->
                            <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                <a class="page-link" href="@Url.Action("DepartmentUsers", new { department = ViewBag.SelectedDepartmentName, searchTerm = ViewBag.SearchTerm, page = currentPage + 1 })" aria-label="Next">
                                    <span aria-hidden="true"> &raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    <div class="text-center text-muted small">
                        صفحة @currentPage من @totalPages
                    </div>
                </div>
            }
        </div>
    </div>
</div>
<div class="modal fade" id="importUsersModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-file-import me-2"></i> استيراد المستخدمين</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <div class="import-icon-container mb-3">
                        <i class="fas fa-file-excel fa-4x text-success"></i>
                        <i class="fas fa-arrow-right fa-2x text-primary mx-3"></i>
                        <i class="fas fa-users fa-4x text-primary"></i>
                    </div>
                    <h5>استيراد مستخدمين من ملف Excel</h5>
                    <p class="text-muted">قم بتحميل ملف Excel يحتوي على بيانات المستخدمين</p>
                </div>

                @using (Html.BeginForm("ImportUsers", "User", FormMethod.Post, new { enctype = "multipart/form-data", id = "importUsersForm" }))
                {
                    <div class="mb-3">
                        <div class="custom-file-upload">
                            <div class="input-group">
                                <input type="file" name="file" id="importUsersFile" class="form-control" accept=".xlsx,.xls" required />
                                <label for="importUsersFile" class="input-group-text">
                                    <i class="fas fa-upload"></i>
                                </label>
                            </div>
                            <small class="form-text text-muted">الملفات المدعومة: .xlsx, .xls</small>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <h6 class="fw-bold"><i class="fas fa-info-circle me-2"></i>بيانات الاستيراد</h6>
                        <ul class="mb-0 small">
                            <li><strong>البريد الإلكتروني (مطلوب)</strong>: يستخدم كاسم المستخدم أيضاً</li>
                            <li><strong>الاسم الكامل (مطلوب)</strong></li>
                            <li><strong>كلمة المرور</strong>: اختيارية (الافتراضية: Password123!)</li>
                            <li><strong>الأدوار</strong>:  (مثال: User, Supervisor)</li>
                        </ul>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="@Url.Action("DownloadUserTemplate", "User")" class="btn btn-outline-primary">
                            <i class="fas fa-download me-1"></i> تنزيل نموذج للاستيراد
                        </a>
                    </div>
                }
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> إلغاء
                </button>
                <button type="submit" form="importUsersForm" class="btn btn-primary">
                    <i class="fas fa-file-import me-1"></i> استيراد
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize tooltips
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
            const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl))

            // Initialize DataTable with RTL support
            $('#departmentUsersTable').DataTable({
                "paging": false, // Disable DataTables pagination since we're using server-side
                "ordering": true,
                "info": false, // Disable info since we show it in our custom pagination
                "searching": false, // We already have a custom search box
                "responsive": true,
                "language": {
                    "emptyTable": "لم يتم العثور على مستخدمين",
                    "zeroRecords": "لم يتم العثور على مستخدمين مطابقين",
                    "info": "عرض _START_ إلى _END_ من _TOTAL_ مستخدم",
                    "infoEmpty": "عرض 0 إلى 0 من 0 مستخدم",
                    "infoFiltered": "(تمت تصفيته من _MAX_ مستخدم إجمالي)"
                },
                "autoWidth": false,
                "columnDefs": [
                    { "targets": [0, 1, 2, 3, 4], "className": "align-middle" },
                    { "targets": -1, "className": "text-center align-middle", "orderable": false }
                ]
            });

            // Add confirmation dialog for delete buttons
            $('.delete-user').on('click', function(e) {
                if (!confirm('هل أنت متأكد من رغبتك في حذف هذا المستخدم؟ لا يمكن التراجع عن هذا الإجراء.')) {
                    e.preventDefault();
                }
            });
        });

        // AJAX approve user
        $(document).on('click', '.approve-user-btn', function () {
            var userId = $(this).data('user-id');
            var $row = $('#user-row-' + userId);
            $.ajax({
                url: '@Url.Action("ApproveUserAjax", "User")',
                type: 'POST',
                data: { id: userId },
                success: function (html) {
                    $row.replaceWith(html);
                },
                error: function () {
                    alert('حدث خطأ أثناء الموافقة على المستخدم.');
                }
            });
        });
    </script>

    <style>
        /* RTL specific adjustments */
        .ml-1, .ml-2, .ml-3, .ml-4 {
            margin-left: 0 !important;
        }

        .ml-1 {
            margin-right: 0.25rem !important;
        }

        .ml-2 {
            margin-right: 0.5rem !important;
        }

        .ml-3 {
            margin-right: 1rem !important;
        }

        /* Avatar style */
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }

        /* Action buttons styling */
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }

            .action-buttons .btn {
                width: 36px;
                height: 36px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s;
            }

                .action-buttons .btn:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }

        /* Empty state styling */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        /* Pagination RTL fixes */
        .pagination {
            direction: ltr;
        }

        /* Table responsive fixes for RTL */
        .table-responsive {
            direction: rtl;
        }

        /* Email alignment in RTL */
        [dir="rtl"] td[dir="ltr"] {
            text-align: left !important;
        }
    </style>
}