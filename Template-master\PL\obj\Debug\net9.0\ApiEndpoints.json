[{"ContainingType": "WebhookController", "Method": "Verify", "RelativePath": "webhook", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "hub.mode", "Type": "System.String", "IsRequired": false}, {"Name": "hub.challenge", "Type": "System.String", "IsRequired": false}, {"Name": "hub.verify_token", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebhookController", "Method": "Receive", "RelativePath": "webhook", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "payload", "Type": "System.Text.Json.JsonElement", "IsRequired": true}], "ReturnTypes": []}]