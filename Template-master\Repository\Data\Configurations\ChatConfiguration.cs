using Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Repository.Data.Configurations
{
    public class ChatConfiguration : IEntityTypeConfiguration<Chat>
    {
        public void Configure(EntityTypeBuilder<Chat> builder)
        {
            builder.HasKey(c => c.Id);

            builder.Property(c => c.CustomerName)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(c => c.CustomerPhone)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(c => c.CustomerEmail)
                .HasMaxLength(100);

            builder.Property(c => c.CustomerAvatar)
                .IsRequired()
                .HasMaxLength(2);

            builder.Property(c => c.Status)
                .IsRequired()
                .HasMaxLength(20)
                .HasDefaultValue("active");

            builder.Property(c => c.CreatedAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");

            builder.Property(c => c.LastMessageAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");

            builder.Property(c => c.LastMessage)
                .HasMaxLength(500);

            builder.Property(c => c.UnreadCount)
                .HasDefaultValue(0);

            builder.Property(c => c.IsArchived)
                .HasDefaultValue(false);

            builder.Property(c => c.TotalTickets)
                .HasDefaultValue(0);

            builder.Property(c => c.ResolvedTickets)
                .HasDefaultValue(0);

            builder.Property(c => c.Notes)
                .HasMaxLength(500);

            // Relationships
            builder.HasMany(c => c.Messages)
                .WithOne(m => m.Chat)
                .HasForeignKey(m => m.ChatId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(c => c.AssignedUser)
                .WithMany()
                .HasForeignKey(c => c.AssignedUserId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(c => c.CustomerPhone)
                .IsUnique();

            builder.HasIndex(c => c.Status);

            builder.HasIndex(c => c.AssignedUserId);

            builder.HasIndex(c => c.LastMessageAt);

            builder.HasIndex(c => c.IsArchived);
        }
    }
}
