using Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Repository.Data.Configurations
{
    public class ChatConfiguration : IEntityTypeConfiguration<Chat>
    {
        public void Configure(EntityTypeBuilder<Chat> builder)
        {
            builder.HasKey(c => c.Id);



            builder.Property(c => c.LastMessageAt)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()");

            builder.Property(c => c.LastMessage)
                .HasMaxLength(500);

            builder.Property(c => c.UnreadCount)
                .HasDefaultValue(0);

 
            builder.Property(c => c.Notes)
                .HasMaxLength(500);

            // Relationships
            builder.HasMany(c => c.Messages)
                .WithOne(m => m.Chat)
                .HasForeignKey(m => m.ChatId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(c => c.AssignedUser)
                .WithMany()
                .HasForeignKey(c => c.AssignedUserId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(c => c.Customer)
                .WithOne()
                .OnDelete(DeleteBehavior.Cascade);


            builder.HasIndex(c => c.AssignedUserId);

            builder.HasIndex(c => c.LastMessageAt);


        }
    }
}
