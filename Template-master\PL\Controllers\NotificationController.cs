
using Core;
using Core.Entities;
using Core.Specifications;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Pl.Models.NotificationViewModel;
using Core.Specifications.NotificationSpec;


namespace PL.Controllers;

[Authorize]
public class NotificationController : Controller
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly UserManager<User> _userManager;

    public NotificationController(
        IUnitOfWork unitOfWork,
        UserManager<User> userManager)
    {
        this._unitOfWork = unitOfWork;
        _userManager = userManager;
    }

    public async Task<IActionResult> Index()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return RedirectToAction("Login", "Auth");
        }

        // Check if user is Admin or Manager
        bool isAdminOrManager = User.IsInRole("Admin") || User.IsInRole("Manager");

        IEnumerable<Notification> notifications;

        if (isAdminOrManager)
        {
            // For Admin and Manager, get all notifications
            notifications = await _unitOfWork.GetRepository<Notification>().GetAllAsync();
        }
        else
        {
            // For other roles, get only their own notifications
            // notifications = await _notificationService.GetByUserIdAsync(user.Id);
            var spec = new GetByUserId(user.Id) ;
            notifications = await _unitOfWork.GetRepository<Notification>().GetAllSpecAsync(spec);
        }

        var viewModel = notifications.Select(n =>
        {
            // For Admin/Manager, add the username to the notification title
            string title = n.Title;
            if (isAdminOrManager && n.User != null && n.UserId != user.Id)
            {
                title = $"{title} ({n.User.UserName})";
            }

            return new NotificationViewModel
            {
                Id = n.Id,
                Title = title,
                Message = n.Message ?? string.Empty,
                TimeAgo = GetTimeAgo(n.CreatedAt),
                IsRead = n.IsRead,
                NotificationType = n.NotificationType ?? string.Empty,
                ActionUrl = n.ActionUrl ?? string.Empty
            };
        }).ToList();

        return View(viewModel);
    }

    [HttpPost]
    public async Task<IActionResult> MarkAsRead(int id, string returnUrl = null)
    {
        // await _notificationService.MarkAsReadAsync(id);
        var notificationRepo =_unitOfWork.GetRepository<Notification>();
        var notification = await notificationRepo.GetByIdAsync(id);
        if (notification != null)
        {
            notification.IsRead = true;
            notificationRepo.Update(notification);
            await _unitOfWork.CompleteAsync();
        }

        if (string.IsNullOrEmpty(returnUrl))
        {
            return RedirectToAction(nameof(Index));
        }
        return Redirect(returnUrl);
    }

    [HttpPost]
    public async Task<IActionResult> MarkAllAsRead()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return RedirectToAction("Login", "Auth");
        }

        // Check if user is Admin or Manager
        bool isAdminOrManager = User.IsInRole("Admin") || User.IsInRole("Manager");

        if (isAdminOrManager)
        {
            // For Admin and Manager, mark all notifications as read
           // await _notificationService.MarkAllAsReadAsync();
            var notificationRepo = _unitOfWork.GetRepository<Notification>();
            var allNotifications = await notificationRepo.GetAllAsync();
            foreach (var notification in allNotifications)
            {
                notification.IsRead = true;
                notificationRepo.Update(notification);
            }
            await _unitOfWork.CompleteAsync();
        }
        else
        {
            // For other roles, mark only their own notifications as read
          //  await _notificationService.MarkAllAsReadAsync(user.Id);
            var spec = new GetByUserId(user.Id);
            var notificationRepo = _unitOfWork.GetRepository<Notification>();
            var userNotifications = await notificationRepo.GetAllSpecAsync(spec);
            foreach (var notification in userNotifications)
            {
                notification.IsRead = true;
                notificationRepo.Update(notification);
            }
            await _unitOfWork.CompleteAsync();
        }

        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    public async Task<IActionResult> Delete(int id)
    {
      //  await _notificationService.DeleteAsync(id);
        var notificationRepo = _unitOfWork.GetRepository<Notification>();
        var notification = await notificationRepo.GetByIdAsync(id);
        if (notification != null)
        {
            notificationRepo.Delete(notification);
            await _unitOfWork.CompleteAsync();
        }
        return RedirectToAction(nameof(Index));
    }

    // Get unread notifications for the navbar dropdown
    [HttpGet]
   // [Cached(15)]
    public async Task<IActionResult> GetUnreadNotifications()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Json(new { success = false, error = "User not authenticated" });
        }

        // Check if user is Admin or Manager to show all notifications
        bool isAdminOrManager = User.IsInRole("Admin") || User.IsInRole("Manager");

        // Get notifications based on role
        IEnumerable<Notification> notifications;
        int count;

        if (isAdminOrManager)
        {
            // For Admin and Manager, get all unread notifications
            //notifications = await _notificationService.GetAllUnreadAsync();
            //count = await _notificationService.GetAllUnreadCountAsync();
           count = await _unitOfWork.GetRepository<Notification>().CountSpecAsync(new Specification<Notification>(n => !n.IsRead));
              notifications = await _unitOfWork.GetRepository<Notification>().GetAllSpecAsync(new Specification<Notification>(n => !n.IsRead));
        }
        else
        {
            // For other roles, get only their own notifications
            //notifications = await _notificationService.GetUnreadByUserIdAsync(user.Id);
            //count = await _notificationService.GetUnreadCountAsync(user.Id);
            var spec = new GetByUserId( user.Id , false);
            count = await _unitOfWork.GetRepository<Notification>().CountSpecAsync(spec);
            notifications = await _unitOfWork.GetRepository<Notification>().GetAllSpecAsync(spec);
        }

        var viewModel = notifications.Select(n => {
            // Format message if it contains a list of asset tags
            string formattedMessage = n.Message;

            // Check if the message contains asset tags list
            if (n.Message != null && n.Message.Contains("[") && n.Message.Contains("]"))
            {
                try
                {
                    // Try to extract and format the asset tags list
                    var startIndex = n.Message.IndexOf('[');
                    var endIndex = n.Message.IndexOf(']', startIndex);

                    if (startIndex >= 0 && endIndex > startIndex)
                    {
                        var assetTagsString = n.Message.Substring(startIndex + 1, endIndex - startIndex - 1);
                        var assetTags = assetTagsString.Split(',').Select(tag => tag.Trim()).ToList();

                        // Replace the original list with a formatted one
                        var beforeList = n.Message.Substring(0, startIndex);
                        var afterList = n.Message.Substring(endIndex + 1);

                        // Format the asset tags as a bulleted list
                        var formattedList = "<ul style='padding-right: 20px; margin: 5px 0;'>";
                        foreach (var tag in assetTags)
                        {
                            formattedList += $"<li>{tag}</li>";
                        }
                        formattedList += "</ul>";

                        formattedMessage = beforeList + formattedList + afterList;
                    }
                }
                catch
                {
                    // If any error occurs during formatting, use the original message
                    formattedMessage = n.Message;
                }
            }

            // For Admin/Manager, add the username to the notification title
            string title = n.Title ?? string.Empty;
            if (isAdminOrManager && n.User != null && n.UserId != user.Id)
            {
                title = $"{title} ({n.User.UserName})";
            }

            return new NotificationViewModel
            {
                Id = n.Id,
                Title = title,
                Message = formattedMessage ?? string.Empty,
                TimeAgo = GetTimeAgo(n.CreatedAt),
                IsRead = n.IsRead,
                NotificationType = n.NotificationType ?? string.Empty,
                ActionUrl = n.ActionUrl ?? string.Empty
            };
        }).ToList();

        return Json(new { success = true, notifications = viewModel, count = count });
    }

    private string GetTimeAgo(DateTime dateTime)
    {
        var span = DateTime.UtcNow - dateTime;

        if (span.Days > 365)
        {
            return $"{span.Days / 365} year{(span.Days / 365 == 1 ? "" : "s")} ago";
        }
        if (span.Days > 30)
        {
            return $"{span.Days / 30} month{(span.Days / 30 == 1 ? "" : "s")} ago";
        }
        if (span.Days > 0)
        {
            return $"{span.Days} day{(span.Days == 1 ? "" : "s")} ago";
        }
        if (span.Hours > 0)
        {
            return $"{span.Hours} hour{(span.Hours == 1 ? "" : "s")} ago";
        }
        if (span.Minutes > 0)
        {
            return $"{span.Minutes} minute{(span.Minutes == 1 ? "" : "s")} ago";
        }
        return "Just now";
    }
}