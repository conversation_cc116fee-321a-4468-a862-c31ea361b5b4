@{
    ViewData["Title"] = "WhatsApp Chat";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .whatsapp-container {
        height: calc(100vh - 120px);
        background: #f0f2f5;
        display: flex;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .chat-sidebar {
        width: 350px;
        background: white;
        border-right: 1px solid #e9ecef;
        display: flex;
        flex-direction: column;
    }

    .sidebar-header {
        padding: 20px;
        background: #00a884;
        color: white;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .sidebar-header h5 {
        margin: 0;
        font-weight: 600;
    }

    .search-box {
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
    }

    .search-box input {
        width: 100%;
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 25px;
        outline: none;
        font-size: 14px;
    }

    .chat-list {
        flex: 1;
        overflow-y: auto;
    }

    .chat-item {
        padding: 15px 20px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s;
        display: flex;
        align-items: center;
    }

    .chat-item:hover {
        background: #f5f5f5;
    }

    .chat-item.active {
        background: #e3f2fd;
        border-right: 3px solid #00a884;
    }

    .chat-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #00a884;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-left: 15px;
        font-size: 18px;
    }

    .chat-info {
        flex: 1;
    }

    .chat-name {
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }

    .chat-last-message {
        color: #666;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .chat-time {
        font-size: 12px;
        color: #999;
        margin-bottom: 5px;
    }

    .unread-count {
        background: #00a884;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
    }

    .chat-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #e5ddd5;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><defs><pattern id="pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23pattern)"/></svg>');
    }

    .chat-header {
        background: white;
        padding: 15px 20px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .chat-header-info {
        display: flex;
        align-items: center;
    }

    .chat-header-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #00a884;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-left: 15px;
    }

    .chat-header-details h6 {
        margin: 0;
        font-weight: 600;
        color: #333;
    }

    .chat-header-details small {
        color: #666;
    }

    .chat-actions {
        display: flex;
        gap: 10px;
    }

    .chat-actions button {
        background: none;
        border: none;
        color: #666;
        font-size: 18px;
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        transition: background-color 0.2s;
    }

    .chat-actions button:hover {
        background: #f0f0f0;
    }

    .chat-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .message {
        max-width: 70%;
        padding: 10px 15px;
        border-radius: 15px;
        position: relative;
        word-wrap: break-word;
    }

    .message.sent {
        background: #dcf8c6;
        align-self: flex-end;
        margin-right: 10px;
    }

    .message.received {
        background: white;
        align-self: flex-start;
        margin-left: 10px;
    }

    .message-time {
        font-size: 11px;
        color: #666;
        margin-top: 5px;
        text-align: left;
    }

    .message.sent .message-time {
        text-align: right;
    }

    .chat-input {
        background: white;
        padding: 15px 20px;
        border-top: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .chat-input input {
        flex: 1;
        padding: 12px 20px;
        border: 1px solid #ddd;
        border-radius: 25px;
        outline: none;
        font-size: 14px;
    }

    .chat-input button {
        background: #00a884;
        color: white;
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .chat-input button:hover {
        background: #008f6f;
    }

    .empty-chat {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #666;
        text-align: center;
    }

    .empty-chat i {
        font-size: 80px;
        margin-bottom: 20px;
        color: #ddd;
    }

 /*    media (max-width: 768px) {
        .whatsapp-container {
            height: calc(100vh - 80px);
        }
        
        .chat-sidebar {
            width: 100%;
            position: absolute;
            z-index: 1000;
            height: 100%;
        }
        
        .chat-main {
            width: 100%;
        }
        
        .chat-sidebar.hidden {
            display: none;
        }
    } */
</style>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">WhatsApp Chat</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-left">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">الرئيسية</a></li>
                    <li class="breadcrumb-item active">WhatsApp</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="whatsapp-container">
            <!-- Chat Sidebar -->
            <div class="chat-sidebar" id="chatSidebar">
                <div class="sidebar-header">
                    <h5>المحادثات</h5>
                    <div>
                        <button class="btn btn-sm btn-light" onclick="showServiceCustomers()">
                            <i class="fas fa-users"></i>
                        </button>
                        <button class="btn btn-sm btn-light" onclick="showSettings()">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
                
                <div class="search-box">
                    <input type="text" placeholder="البحث في المحادثات..." id="searchChats">
                </div>
                
                <div class="chat-list" id="chatList">
                    <!-- Chat items will be populated here -->
                </div>
            </div>

            <!-- Chat Main Area -->
            <div class="chat-main" id="chatMain">
                <div class="empty-chat">
                    <i class="fab fa-whatsapp"></i>
                    <h4>مرحباً بك في WhatsApp</h4>
                    <p>اختر محادثة لبدء المراسلة</p>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    // Static chat data
    const chats = [
        {
            id: 1,
            name: "أحمد محمد",
            lastMessage: "شكراً لك على الخدمة الممتازة",
            time: "10:30 ص",
            unread: 2,
            avatar: "أ",
            messages: [
                { text: "مرحباً، أحتاج مساعدة في المنتج", time: "10:25 ص", sent: false },
                { text: "أهلاً وسهلاً، كيف يمكنني مساعدتك؟", time: "10:26 ص", sent: true },
                { text: "المنتج لا يعمل بشكل صحيح", time: "10:28 ص", sent: false },
                { text: "سأقوم بفحص المشكلة فوراً", time: "10:29 ص", sent: true },
                { text: "شكراً لك على الخدمة الممتازة", time: "10:30 ص", sent: false }
            ]
        },
        {
            id: 2,
            name: "فاطمة علي",
            lastMessage: "متى سيتم التسليم؟",
            time: "09:45 ص",
            unread: 0,
            avatar: "ف",
            messages: [
                { text: "السلام عليكم", time: "09:40 ص", sent: false },
                { text: "وعليكم السلام ورحمة الله", time: "09:41 ص", sent: true },
                { text: "متى سيتم التسليم؟", time: "09:45 ص", sent: false }
            ]
        },
        {
            id: 3,
            name: "محمد حسن",
            lastMessage: "تم استلام الطلب بنجاح",
            time: "أمس",
            unread: 0,
            avatar: "م",
            messages: [
                { text: "هل تم شحن الطلب؟", time: "أمس 2:30 م", sent: false },
                { text: "نعم، تم الشحن وسيصل غداً", time: "أمس 2:35 م", sent: true },
                { text: "تم استلام الطلب بنجاح", time: "أمس 4:20 م", sent: false }
            ]
        },
        {
            id: 4,
            name: "سارة أحمد",
            lastMessage: "ممتاز، شكراً لكم",
            time: "أمس",
            unread: 1,
            avatar: "س",
            messages: [
                { text: "أريد تغيير عنوان التسليم", time: "أمس 11:00 ص", sent: false },
                { text: "بالطبع، ما هو العنوان الجديد؟", time: "أمس 11:05 ص", sent: true },
                { text: "شارع الملك فهد، الرياض", time: "أمس 11:10 ص", sent: false },
                { text: "تم تحديث العنوان بنجاح", time: "أمس 11:15 ص", sent: true },
                { text: "ممتاز، شكراً لكم", time: "أمس 11:20 ص", sent: false }
            ]
        }
    ];

    let currentChatId = null;

    // Initialize chat list
    function initializeChatList() {
        const chatList = document.getElementById('chatList');
        chatList.innerHTML = '';
        
        chats.forEach(chat => {
            const chatItem = document.createElement('div');
            chatItem.className = 'chat-item';
            chatItem.onclick = () => openChat(chat.id);
            
            chatItem.innerHTML = `
                <div class="chat-avatar">${chat.avatar}</div>
                <div class="chat-info">
                    <div class="chat-name">${chat.name}</div>
                    <div class="chat-last-message">${chat.lastMessage}</div>
                </div>
                <div style="text-align: center;">
                    <div class="chat-time">${chat.time}</div>
                    ${chat.unread > 0 ? `<div class="unread-count">${chat.unread}</div>` : ''}
                </div>
            `;
            
            chatList.appendChild(chatItem);
        });
    }

    // Open chat
    function openChat(chatId) {
        currentChatId = chatId;
        const chat = chats.find(c => c.id === chatId);
        
        // Update active chat in sidebar
        document.querySelectorAll('.chat-item').forEach(item => item.classList.remove('active'));
        event.currentTarget.classList.add('active');
        
        // Update main chat area
        const chatMain = document.getElementById('chatMain');
        chatMain.innerHTML = `
            <div class="chat-header">
                <div class="chat-header-info">
                    <div class="chat-header-avatar">${chat.avatar}</div>
                    <div class="chat-header-details">
                        <h6>${chat.name}</h6>
                        <small>متصل الآن</small>
                    </div>
                </div>
                <div class="chat-actions">
                    <button><i class="fas fa-phone"></i></button>
                    <button><i class="fas fa-video"></i></button>
                    <button><i class="fas fa-ellipsis-v"></i></button>
                </div>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                ${chat.messages.map(msg => `
                    <div class="message ${msg.sent ? 'sent' : 'received'}">
                        ${msg.text}
                        <div class="message-time">${msg.time}</div>
                    </div>
                `).join('')}
            </div>
            
            <div class="chat-input">
                <button><i class="fas fa-paperclip"></i></button>
                <input type="text" placeholder="اكتب رسالة..." id="messageInput" onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()"><i class="fas fa-paper-plane"></i></button>
            </div>
        `;
        
        // Scroll to bottom
        setTimeout(() => {
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, 100);
    }

    // Send message
    function sendMessage() {
        const input = document.getElementById('messageInput');
        const message = input.value.trim();
        
        if (message && currentChatId) {
            const chat = chats.find(c => c.id === currentChatId);
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
            
            // Add message to chat
            chat.messages.push({
                text: message,
                time: timeString,
                sent: true
            });
            
            // Update last message
            chat.lastMessage = message;
            chat.time = timeString;
            
            // Refresh chat
            openChat(currentChatId);
            initializeChatList();
            
            input.value = '';
        }
    }

    // Handle enter key
    function handleKeyPress(event) {
        if (event.key === 'Enter') {
            sendMessage();
        }
    }

    // Search chats
    document.getElementById('searchChats').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const chatItems = document.querySelectorAll('.chat-item');
        
        chatItems.forEach(item => {
            const chatName = item.querySelector('.chat-name').textContent.toLowerCase();
            if (chatName.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // Show service customers (will be implemented in separate view)
    function showServiceCustomers() {
        window.location.href = '@Url.Action("ServiceCustomers", "Whatsapp")';
    }

    // Show settings (will be implemented in separate view)
    function showSettings() {
        window.location.href = '@Url.Action("Settings", "Whatsapp")';
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        initializeChatList();
    });
</script>
