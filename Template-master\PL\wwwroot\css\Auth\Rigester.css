﻿
.register-container {
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
}

.card {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    overflow: hidden;
    animation: fadeIn 0.8s ease-in-out;
}

@@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-control {
    border-radius: 0 8px 8px 0;
    padding: 12px;
    transition: all 0.3s;
}

    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

.input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    border-radius: 8px 0 0 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #2937f0, #9f1ae2);
    border: none;
    border-radius: 8px;
    transition: all 0.3s;
}

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
        background: linear-gradient(45deg, #1e2bd1, #8614c0);
    }

/* Fix for RTL with input fields */
input[dir="ltr"] {
    text-align: left;
}

.validation-summary-errors ul {
    list-style-type: none;
    padding-right: 10px;
}

.validation-summary-errors li {
    margin-bottom: 5px;
}

/* Fix for RTL input groups */
.input-group > .form-control:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-right: -1px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}


