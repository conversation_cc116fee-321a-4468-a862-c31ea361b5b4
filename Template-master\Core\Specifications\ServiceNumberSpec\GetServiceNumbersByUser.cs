using Core.Entities;

namespace Core.Specifications.ServiceNumberSpec
{
    public class GetServiceNumbersByUser : Specification<ServiceNumber>
    {
        public GetServiceNumbersByUser(string userId) : base(sn => sn.AssignedUsers.Any(u => u.Id == userId))
        {
            includes.Add(sn => sn.CreatedByUser);
            includes.Add(sn => sn.AssignedUsers);
            OrderBy = sn => sn.DisplayOrder;
        }

        public GetServiceNumbersByUser(string userId, bool isActive) : base(sn => sn.AssignedUsers.Any(u => u.Id == userId) && sn.IsActive == isActive)
        {
            includes.Add(sn => sn.CreatedByUser);
            includes.Add(sn => sn.AssignedUsers);
            OrderBy = sn => sn.DisplayOrder;
        }
    }
}
